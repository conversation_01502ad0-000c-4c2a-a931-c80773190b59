#!/usr/bin/env python3
"""
Database reset script for Carbon Regulation News Application.
This script completely resets the database, removing all data and recreating tables.
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / 'app'))

from app.core.database import DatabaseManager, get_db_session
from app.core.models import NewsArticle, TaskExecution, TaskResult, NotificationLog
from sqlalchemy import text

def reset_database():
    """Reset the database by dropping all tables and recreating them."""
    print("🗄️ RESETTING DATABASE")
    print("=" * 60)
    
    try:
        # Get database file path
        db_file = Path("carbon_news.db")
        
        if db_file.exists():
            print(f"📁 Found existing database: {db_file}")
            print("🗑️ Removing existing database file...")
            db_file.unlink()
            print("✅ Database file removed")
        else:
            print("📁 No existing database file found")
        
        # Initialize fresh database
        print("🔧 Initializing fresh database...")
        DatabaseManager.initialize()
        print("✅ Database initialized successfully")
        
        # Verify tables are created
        with get_db_session() as session:
            # Check if tables exist by trying to count records
            article_count = session.query(NewsArticle).count()
            task_count = session.query(TaskExecution).count()
            result_count = session.query(TaskResult).count()
            notification_count = session.query(NotificationLog).count()
            
            print(f"📊 Database verification:")
            print(f"  Articles: {article_count}")
            print(f"  Task Executions: {task_count}")
            print(f"  Task Results: {result_count}")
            print(f"  Notification Logs: {notification_count}")
        
        print("\n✅ DATABASE RESET COMPLETE!")
        print("🚀 Ready for fresh data collection and processing")
        
        return True
        
    except Exception as e:
        print(f"❌ Database reset failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("🔄 Carbon Regulation News - Database Reset")
    print("⚠️  WARNING: This will delete ALL existing data!")
    
    # Ask for confirmation
    response = input("\nAre you sure you want to reset the database? (yes/no): ")
    
    if response.lower() in ['yes', 'y']:
        success = reset_database()
        if success:
            print("\n🎯 Next steps:")
            print("  1. Run: python app/scripts/run_daily_task.py --task-type full-pipeline")
            print("  2. Or use the API to trigger tasks")
            sys.exit(0)
        else:
            sys.exit(1)
    else:
        print("❌ Database reset cancelled")
        sys.exit(0)

if __name__ == "__main__":
    main()
