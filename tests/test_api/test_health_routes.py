"""
Unit tests for health check API routes.
"""

import pytest
from unittest.mock import patch, Mock
from datetime import datetime, timezone


class TestHealthRoutes:
    """Test cases for health check endpoints."""
    
    def test_health_check_success(self, client, db_session, sample_news_article):
        """Test successful health check."""
        with patch('app.api.routes.health.DatabaseManager.health_check', return_value=True):
            response = client.get("/health/")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] == True
            assert data["status"] == "healthy"
            assert data["database_connected"] == True
            assert data["total_articles"] >= 1  # At least the sample article
            assert "uptime_seconds" in data
            assert "timestamp" in data
    
    def test_health_check_database_failure(self, client, db_session):
        """Test health check with database failure."""
        with patch('app.api.routes.health.DatabaseManager.health_check', return_value=False):
            response = client.get("/health/")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] == True
            assert data["status"] == "unhealthy"
            assert data["database_connected"] == False
            assert data["total_articles"] == 0
    
    def test_health_check_exception(self, client, db_session):
        """Test health check with exception."""
        with patch('app.api.routes.health.DatabaseManager.health_check', side_effect=Exception("DB Error")):
            response = client.get("/health/")
            
            assert response.status_code == 500
            data = response.json()
            
            assert data["success"] == False
            assert "Health check failed" in data["message"]
    
    def test_get_system_statistics(self, client, db_session, sample_task_execution, processed_news_article):
        """Test system statistics endpoint."""
        response = client.get("/health/statistics")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] == True
        assert "statistics" in data
        
        stats = data["statistics"]
        assert "task_stats" in stats
        assert "news_stats" in stats
        assert "database_health" in stats
        assert "last_updated" in stats
        
        # Check task statistics
        task_stats = stats["task_stats"]
        assert task_stats["total_executions"] >= 1
        assert task_stats["successful_executions"] >= 0
        assert task_stats["failed_executions"] >= 0
        assert "success_rate" in task_stats
        
        # Check news statistics
        news_stats = stats["news_stats"]
        assert news_stats["total_articles"] >= 1
        assert news_stats["processed_articles"] >= 1
        assert "sources_count" in news_stats
        assert "categories" in news_stats
    
    def test_get_system_statistics_exception(self, client, db_session):
        """Test system statistics with database exception."""
        with patch('app.core.database.get_db', side_effect=Exception("DB Error")):
            response = client.get("/health/statistics")
            
            assert response.status_code == 500
            data = response.json()
            
            assert data["success"] == False
            assert "Failed to get statistics" in data["message"]
    
    def test_check_database_health_success(self, client):
        """Test database health check endpoint."""
        with patch('app.api.routes.health.DatabaseManager.health_check', return_value=True):
            response = client.get("/health/database")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] == True
            assert data["status"] == "connected"
            assert "Database is healthy" in data["message"]
    
    def test_check_database_health_failure(self, client):
        """Test database health check with failure."""
        with patch('app.api.routes.health.DatabaseManager.health_check', return_value=False):
            response = client.get("/health/database")
            
            assert response.status_code == 503
            data = response.json()
            
            assert data["success"] == False
            assert "Database is not accessible" in data["message"]
    
    def test_check_database_health_exception(self, client):
        """Test database health check with exception."""
        with patch('app.api.routes.health.DatabaseManager.health_check', side_effect=Exception("DB Error")):
            response = client.get("/health/database")
            
            assert response.status_code == 503
            data = response.json()
            
            assert data["success"] == False
            assert "Database health check failed" in data["message"]
    
    def test_check_scheduler_status(self, client):
        """Test scheduler status endpoint."""
        response = client.get("/health/scheduler")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] == True
        assert "scheduler_running" in data
        assert "next_scheduled_run" in data
        assert "last_execution" in data
        assert "timestamp" in data


class TestMainAppRoutes:
    """Test cases for main application routes."""
    
    def test_root_endpoint(self, client):
        """Test root endpoint."""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["message"] == "Carbon Regulation News API"
        assert data["version"] == "1.0.0"
        assert "uptime_seconds" in data
        assert "endpoints" in data
        assert data["endpoints"]["health"] == "/health"
        assert data["endpoints"]["tasks"] == "/tasks"
        assert data["endpoints"]["news"] == "/news"
    
    def test_ping_endpoint(self, client):
        """Test ping endpoint for load balancers."""
        response = client.get("/ping")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "ok"
        assert "timestamp" in data
    
    def test_openapi_docs(self, client):
        """Test OpenAPI documentation endpoint."""
        response = client.get("/docs")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_openapi_json(self, client):
        """Test OpenAPI JSON schema endpoint."""
        response = client.get("/openapi.json")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["info"]["title"] == "Carbon Regulation News API"
        assert data["info"]["version"] == "1.0.0"
        assert "paths" in data
        assert "components" in data


class TestErrorHandling:
    """Test cases for error handling."""
    
    def test_404_error(self, client):
        """Test 404 error handling."""
        response = client.get("/nonexistent-endpoint")
        
        assert response.status_code == 404
    
    def test_method_not_allowed(self, client):
        """Test 405 method not allowed."""
        response = client.post("/health/")
        
        assert response.status_code == 405
    
    def test_global_exception_handler(self, client):
        """Test global exception handler."""
        # This would require creating an endpoint that raises an exception
        # For now, we'll test that the handler exists and is properly configured
        from app.api.main import app
        from starlette.exceptions import HTTPException

        # Check that exception handlers are registered
        assert Exception in app.exception_handlers
        assert HTTPException in app.exception_handlers  # HTTPException handler


class TestCORSMiddleware:
    """Test cases for CORS middleware."""
    
    def test_cors_headers(self, client):
        """Test CORS headers are present."""
        # Add Origin header to trigger CORS behavior
        headers = {"Origin": "https://example.com"}
        response = client.get("/", headers=headers)

        # CORS headers should be present
        assert "access-control-allow-origin" in response.headers
    
    def test_cors_preflight(self, client):
        """Test CORS preflight request."""
        headers = {
            "Origin": "https://example.com",
            "Access-Control-Request-Method": "GET",
            "Access-Control-Request-Headers": "Content-Type"
        }
        
        response = client.options("/health/", headers=headers)
        
        assert response.status_code == 200
        assert "access-control-allow-origin" in response.headers


class TestRequestLogging:
    """Test cases for request logging middleware."""
    
    def test_request_logging(self, client):
        """Test that requests are logged."""
        with patch('app.api.main.logger') as mock_logger:
            response = client.get("/")

            assert response.status_code == 200
            
            # Verify logging calls were made
            assert mock_logger.info.call_count >= 2  # Start and completion logs
            
            # Check log content
            log_calls = mock_logger.info.call_args_list
            start_log = log_calls[0][0][0]
            completion_log = log_calls[1][0][0]
            
            assert "HTTP request started" in start_log
            assert "HTTP request completed" in completion_log


class TestApplicationLifespan:
    """Test cases for application lifespan events."""
    
    def test_startup_event(self):
        """Test application startup event."""
        with patch('app.core.database.DatabaseManager.initialize') as mock_init:
            from app.api.main import create_app
            
            # Creating the app should not call initialize yet
            app = create_app()
            mock_init.assert_not_called()
            
            # The lifespan context manager would call initialize
            # This is tested implicitly when the test client starts up
    
    def test_database_initialization_failure(self):
        """Test handling of database initialization failure."""
        with patch('app.core.database.DatabaseManager.initialize', side_effect=Exception("DB Init Error")):
            # This would be tested in integration tests where the full app starts up
            pass
