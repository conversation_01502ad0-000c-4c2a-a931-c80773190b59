"""
Task scheduler service for managing automated news collection and processing.
"""

import threading
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
import schedule
from sqlalchemy.orm import Session

from ..core.config import get_settings
from ..core.logging import LoggerMixin
from ..core.models import TaskExecution, TaskResult, NewsArticle
from ..core.database import get_db_session
from .news_collector import NewsCollectionService
from .ai_parser import AINewsParsingService
from .notifications import NotificationService


class TaskStatus(str, Enum):
    """Task execution status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"


class TaskType(str, Enum):
    """Task type enumeration."""
    NEWS_COLLECTION = "news_collection"
    AI_PROCESSING = "ai_processing"
    DAILY_SUMMARY = "daily_summary"
    FULL_PIPELINE = "full_pipeline"


class TaskSchedulerService(LoggerMixin):
    """Service for scheduling and executing automated tasks."""
    
    def __init__(self, db_session: Optional[Session] = None):
        """Initialize the task scheduler service."""
        self.settings = get_settings()
        self.db = db_session or get_db_session()
        self.is_running = False
        self._stop_event = threading.Event()
        self._scheduler_thread = None
        
        # Initialize services
        self.news_collector = NewsCollectionService(self.db)
        self.ai_parser = AINewsParsingService(self.db)
        self.notification_service = NotificationService(self.db)
        
        self.log_method_call("__init__")
    
    def start_scheduler(self) -> None:
        """Start the task scheduler in a background thread."""
        if self.is_running:
            self.logger.warning("Scheduler is already running")
            return
        
        self.is_running = True
        self._stop_event.clear()
        
        # Schedule daily tasks
        schedule.every().day.at(self.settings.scheduler.daily_run_time).do(
            self._execute_task_safe,
            TaskType.FULL_PIPELINE,
            "daily_full_pipeline"
        )
        
        # Start scheduler thread
        self._scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self._scheduler_thread.start()
        
        self.logger.info(
            "Task scheduler started",
            daily_run_time=self.settings.scheduler.daily_run_time
        )
    
    def stop_scheduler(self) -> None:
        """Stop the task scheduler."""
        if not self.is_running:
            return
        
        self.is_running = False
        self._stop_event.set()
        schedule.clear()
        
        if self._scheduler_thread and self._scheduler_thread.is_alive():
            self._scheduler_thread.join(timeout=5)
        
        self.logger.info("Task scheduler stopped")
    
    def _run_scheduler(self) -> None:
        """Run the scheduler loop."""
        self.logger.info("Scheduler thread started")
        
        while not self._stop_event.is_set():
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                self.log_error(e, {"context": "scheduler_loop"})
                time.sleep(5)  # Wait a bit before retrying
        
        self.logger.info("Scheduler thread stopped")
    
    def _execute_task_safe(self, task_type: TaskType, task_name: str) -> None:
        """Safely execute a task with error handling."""
        try:
            self.execute_task(task_type, task_name)
        except Exception as e:
            self.log_error(e, {"task_type": task_type.value, "task_name": task_name})
    
    def execute_task(self, task_type: TaskType, task_name: str) -> TaskExecution:
        """Execute a specific task and track its execution."""
        self.log_method_call("execute_task", task_type=task_type.value, task_name=task_name)
        
        # Create task execution record
        task_execution = TaskExecution(
            task_name=task_name,
            task_type=task_type.value,
            status=TaskStatus.PENDING.value,
            started_at=datetime.now(timezone.utc),
            config_snapshot={
                "settings": {
                    "max_articles_per_source": self.settings.news_collector.max_articles_per_source,
                    "default_time_range": self.settings.news_collector.default_time_range,
                    "search_queries": self.settings.news_collector.search_queries,
                    "specific_sources": self.settings.news_collector.specific_sources
                }
            }
        )
        
        self.db.add(task_execution)
        self.db.commit()
        self.db.refresh(task_execution)
        
        start_time = datetime.now(timezone.utc)
        
        try:
            # Update status to running
            task_execution.status = TaskStatus.RUNNING.value
            self.db.commit()
            
            # Execute the appropriate task
            if task_type == TaskType.NEWS_COLLECTION:
                result = self._execute_news_collection_task(task_execution)
            elif task_type == TaskType.AI_PROCESSING:
                result = self._execute_ai_processing_task(task_execution)
            elif task_type == TaskType.DAILY_SUMMARY:
                result = self._execute_daily_summary_task(task_execution)
            elif task_type == TaskType.FULL_PIPELINE:
                result = self._execute_full_pipeline_task(task_execution)
            else:
                raise ValueError(f"Unknown task type: {task_type}")
            
            # Update task execution with success
            end_time = datetime.now(timezone.utc)
            task_execution.status = TaskStatus.SUCCESS.value
            task_execution.completed_at = end_time
            task_execution.duration_seconds = (end_time - start_time).total_seconds()
            task_execution.result_summary = result
            
            self.db.commit()
            
            self.logger.info(
                "Task completed successfully",
                task_id=task_execution.id,
                task_type=task_type.value,
                duration=task_execution.duration_seconds
            )
            
            # Send success notification
            self._send_task_notification(task_execution, result)
            
        except Exception as e:
            # Update task execution with failure
            end_time = datetime.now(timezone.utc)
            task_execution.status = TaskStatus.FAILED.value
            task_execution.completed_at = end_time
            task_execution.duration_seconds = (end_time - start_time).total_seconds()
            task_execution.error_message = str(e)
            task_execution.error_details = {"error_type": type(e).__name__}
            
            self.db.commit()
            
            self.log_error(e, {"task_id": task_execution.id, "task_type": task_type.value})
            
            # Send failure notification
            self._send_task_notification(task_execution, None)
            
            raise
        
        return task_execution
    
    def _execute_news_collection_task(self, task_execution: TaskExecution) -> Dict[str, Any]:
        """Execute news collection task."""
        self.logger.info("Executing news collection task", task_id=task_execution.id)
        
        # Collect and save news
        result = self.news_collector.collect_and_save_news()
        
        # Create task result record
        if result["saved_count"] > 0:
            task_result = TaskResult(
                task_execution_id=task_execution.id,
                result_type="news_collection",
                title=f"Collected {result['saved_count']} new articles",
                summary=f"Successfully collected {result['collected_count']} articles from {len(result['sources'])} sources, saved {result['saved_count']} new articles to database.",
                statistics=result
            )
            self.db.add(task_result)
            self.db.commit()
        
        return result
    
    def _execute_ai_processing_task(self, task_execution: TaskExecution) -> Dict[str, Any]:
        """Execute AI processing task."""
        self.logger.info("Executing AI processing task", task_id=task_execution.id)
        
        # Process unprocessed articles
        result = self.ai_parser.process_unprocessed_articles()
        
        # Create task result record
        if result["processed_count"] > 0:
            task_result = TaskResult(
                task_execution_id=task_execution.id,
                result_type="ai_processing",
                title=f"Processed {result['processed_count']} articles with AI",
                summary=f"Successfully processed {result['processed_count']} articles out of {result['total_unprocessed']} unprocessed articles.",
                statistics=result
            )
            self.db.add(task_result)
            self.db.commit()
        
        return result
    
    def _execute_daily_summary_task(self, task_execution: TaskExecution) -> Dict[str, Any]:
        """Execute daily summary generation task."""
        self.logger.info("Executing daily summary task", task_id=task_execution.id)
        
        # Get today's processed articles
        today = datetime.now(timezone.utc).date()
        articles = self.db.query(NewsArticle).filter(
            NewsArticle.collected_at >= today,
            NewsArticle.is_processed == True
        ).all()
        
        # Generate summary
        summary_result = self.ai_parser.generate_daily_summary(articles)
        
        # Create task result record
        # Use markdown summary if available, otherwise fall back to executive summary
        main_summary = summary_result.get("markdown_summary")
        if not main_summary:
            main_summary = summary_result.get("executive_summary", "No summary available")

        task_result = TaskResult(
            task_execution_id=task_execution.id,
            result_type="daily_summary",
            title=f"Daily Summary - {today.strftime('%Y-%m-%d')}",
            summary=main_summary,
            key_findings=summary_result.get("key_developments", []),
            statistics=summary_result.get("statistics", {}),
            result_metadata=summary_result
        )
        self.db.add(task_result)
        self.db.commit()
        
        return summary_result
    
    def _execute_full_pipeline_task(self, task_execution: TaskExecution) -> Dict[str, Any]:
        """Execute the full pipeline: collection -> processing -> summary."""
        self.logger.info("Executing full pipeline task", task_id=task_execution.id)
        
        pipeline_result = {
            "collection": {},
            "processing": {},
            "summary": {},
            "total_duration": 0
        }
        
        start_time = datetime.now(timezone.utc)
        
        try:
            # Step 1: News Collection
            self.logger.info("Pipeline step 1: News collection")
            collection_result = self.news_collector.collect_and_save_news()
            pipeline_result["collection"] = collection_result
            
            # Step 2: AI Processing
            self.logger.info("Pipeline step 2: AI processing")
            processing_result = self.ai_parser.process_unprocessed_articles()
            pipeline_result["processing"] = processing_result
            
            # Step 3: Daily Summary (only if we have processed articles)
            if processing_result["processed_count"] > 0:
                self.logger.info("Pipeline step 3: Daily summary generation")
                
                # Get today's processed articles
                today = datetime.now(timezone.utc).date()
                articles = self.db.query(NewsArticle).filter(
                    NewsArticle.collected_at >= today,
                    NewsArticle.is_processed == True
                ).all()
                
                summary_result = self.ai_parser.generate_daily_summary(articles)
                pipeline_result["summary"] = summary_result
                
                # Create summary task result
                # Use markdown summary if available, otherwise fall back to executive summary
                main_summary = summary_result.get("markdown_summary")
                if not main_summary:
                    main_summary = summary_result.get("executive_summary", "")

                task_result = TaskResult(
                    task_execution_id=task_execution.id,
                    result_type="daily_summary",
                    title=f"Daily Carbon Regulation News Summary - {today.strftime('%Y-%m-%d')}",
                    summary=main_summary,
                    key_findings=summary_result.get("key_developments", []),
                    statistics=summary_result.get("statistics", {}),
                    result_metadata=summary_result
                )
                self.db.add(task_result)
                self.db.commit()
            
            end_time = datetime.now(timezone.utc)
            pipeline_result["total_duration"] = (end_time - start_time).total_seconds()
            
            self.logger.info(
                "Full pipeline completed successfully",
                collected=collection_result["saved_count"],
                processed=processing_result["processed_count"],
                duration=pipeline_result["total_duration"]
            )
            
            return pipeline_result
            
        except Exception as e:
            end_time = datetime.now(timezone.utc)
            pipeline_result["total_duration"] = (end_time - start_time).total_seconds()
            pipeline_result["error"] = str(e)
            raise
    
    def _send_task_notification(self, task_execution: TaskExecution, result: Optional[Dict[str, Any]]) -> None:
        """Send notification about task completion."""
        try:
            if task_execution.status == TaskStatus.SUCCESS.value:
                self.notification_service.send_task_success_notification(task_execution, result)
            else:
                self.notification_service.send_task_failure_notification(task_execution)
        except Exception as e:
            self.log_error(e, {"context": "notification_sending", "task_id": task_execution.id})
    
    def get_recent_executions(self, limit: int = 50) -> List[TaskExecution]:
        """Get recent task executions."""
        return self.db.query(TaskExecution).order_by(
            TaskExecution.started_at.desc()
        ).limit(limit).all()
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get task execution statistics."""
        total_executions = self.db.query(TaskExecution).count()
        successful_executions = self.db.query(TaskExecution).filter(
            TaskExecution.status == TaskStatus.SUCCESS.value
        ).count()
        failed_executions = self.db.query(TaskExecution).filter(
            TaskExecution.status == TaskStatus.FAILED.value
        ).count()
        
        # Get last execution
        last_execution = self.db.query(TaskExecution).order_by(
            TaskExecution.started_at.desc()
        ).first()
        
        # Get last successful execution
        last_success = self.db.query(TaskExecution).filter(
            TaskExecution.status == TaskStatus.SUCCESS.value
        ).order_by(TaskExecution.started_at.desc()).first()
        
        return {
            "total_executions": total_executions,
            "successful_executions": successful_executions,
            "failed_executions": failed_executions,
            "success_rate": successful_executions / total_executions if total_executions > 0 else 0,
            "last_execution": last_execution.started_at if last_execution else None,
            "last_success": last_success.started_at if last_success else None,
            "scheduler_running": self.is_running
        }
    
    def manual_trigger_task(self, task_type: TaskType, task_name: Optional[str] = None) -> TaskExecution:
        """Manually trigger a task execution."""
        task_name = task_name or f"manual_{task_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.logger.info("Manual task trigger", task_type=task_type.value, task_name=task_name)
        return self.execute_task(task_type, task_name)
    
    def __del__(self):
        """Clean up resources."""
        self.stop_scheduler()
        if hasattr(self, 'db') and self.db:
            self.db.close()
