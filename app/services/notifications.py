"""
Generic notification system with support for webhooks, Slack, and extensible architecture.
"""

import json
import requests
from abc import ABC, abstractmethod
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List, Type
from enum import Enum
from sqlalchemy.orm import Session

from ..core.config import get_settings
from ..core.logging import LoggerMixin
from ..core.models import NotificationLog, TaskExecution
from ..core.database import get_db_session


class NotificationType(str, Enum):
    """Notification type enumeration."""
    WEBHOOK = "webhook"
    SLACK = "slack"
    EMAIL = "email"  # Future implementation


class NotificationStatus(str, Enum):
    """Notification status enumeration."""
    SENT = "sent"
    FAILED = "failed"
    PENDING = "pending"
    RETRY = "retry"


class BaseNotificationChannel(ABC):
    """Abstract base class for notification channels."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    @abstractmethod
    def send(self, subject: str, message: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Send notification. Returns response info."""
        pass
    
    @abstractmethod
    def get_channel_type(self) -> NotificationType:
        """Get the notification channel type."""
        pass
    
    @abstractmethod
    def validate_config(self) -> bool:
        """Validate channel configuration."""
        pass


class WebhookNotificationChannel(BaseNotificationChannel):
    """Webhook notification channel."""
    
    def get_channel_type(self) -> NotificationType:
        return NotificationType.WEBHOOK
    
    def validate_config(self) -> bool:
        """Validate webhook configuration."""
        return "url" in self.config and self.config["url"]
    
    def send(self, subject: str, message: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Send webhook notification."""
        if not self.validate_config():
            raise ValueError("Invalid webhook configuration")
        
        webhook_payload = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "subject": subject,
            "message": message,
            "data": payload
        }
        
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Carbon-Regulation-News/1.0"
        }
        
        # Add custom headers if configured
        if "headers" in self.config:
            headers.update(self.config["headers"])
        
        response = requests.post(
            self.config["url"],
            json=webhook_payload,
            headers=headers,
            timeout=30
        )
        
        return {
            "status_code": response.status_code,
            "response_text": response.text[:1000],  # Limit response text
            "success": response.status_code < 400
        }


class SlackNotificationChannel(BaseNotificationChannel):
    """Slack notification channel using webhooks."""
    
    def get_channel_type(self) -> NotificationType:
        return NotificationType.SLACK
    
    def validate_config(self) -> bool:
        """Validate Slack configuration."""
        return "webhook_url" in self.config and self.config["webhook_url"]
    
    def send(self, subject: str, message: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Send Slack notification."""
        if not self.validate_config():
            raise ValueError("Invalid Slack configuration")
        
        # Format message for Slack
        slack_payload = self._format_slack_message(subject, message, payload)
        
        headers = {
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            self.config["webhook_url"],
            json=slack_payload,
            headers=headers,
            timeout=30
        )
        
        return {
            "status_code": response.status_code,
            "response_text": response.text[:1000],
            "success": response.status_code < 400
        }
    
    def _format_slack_message(self, subject: str, message: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Format message for Slack with rich formatting."""
        
        # Determine color based on payload content
        color = "good"  # green
        if "error" in payload or "failed" in subject.lower():
            color = "danger"  # red
        elif "warning" in payload:
            color = "warning"  # yellow
        
        # Build attachment
        attachment = {
            "color": color,
            "title": subject,
            "text": message,
            "timestamp": int(datetime.now(timezone.utc).timestamp()),
            "footer": "Carbon Regulation News",
            "fields": []
        }
        
        # Add relevant fields from payload
        if "statistics" in payload:
            stats = payload["statistics"]
            if "total_articles" in stats:
                attachment["fields"].append({
                    "title": "Articles Processed",
                    "value": str(stats["total_articles"]),
                    "short": True
                })
            if "processed_count" in stats:
                attachment["fields"].append({
                    "title": "Successfully Processed",
                    "value": str(stats["processed_count"]),
                    "short": True
                })
        
        if "collection" in payload:
            collection = payload["collection"]
            if "saved_count" in collection:
                attachment["fields"].append({
                    "title": "New Articles Collected",
                    "value": str(collection["saved_count"]),
                    "short": True
                })
        
        # Add duration if available
        if "total_duration" in payload:
            duration = payload["total_duration"]
            attachment["fields"].append({
                "title": "Duration",
                "value": f"{duration:.1f} seconds",
                "short": True
            })
        
        return {
            "text": f"📊 {subject}",
            "attachments": [attachment]
        }


class EmailNotificationChannel(BaseNotificationChannel):
    """Email notification channel (skeleton for future implementation)."""
    
    def get_channel_type(self) -> NotificationType:
        return NotificationType.EMAIL
    
    def validate_config(self) -> bool:
        """Validate email configuration."""
        required_fields = ["smtp_host", "smtp_port", "username", "password", "to_email"]
        return all(field in self.config for field in required_fields)
    
    def send(self, subject: str, message: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Send email notification (skeleton implementation)."""
        # TODO: Implement email sending using smtplib
        raise NotImplementedError("Email notifications not yet implemented")


class NotificationService(LoggerMixin):
    """Service for managing and sending notifications through various channels."""
    
    def __init__(self, db_session: Optional[Session] = None):
        """Initialize the notification service."""
        self.settings = get_settings()
        self.db = db_session or get_db_session()
        self.channels: Dict[str, BaseNotificationChannel] = {}
        
        self._initialize_channels()
        self.log_method_call("__init__")
    
    def _initialize_channels(self) -> None:
        """Initialize configured notification channels."""
        # Initialize webhook channel
        if self.settings.notifications.webhook_url:
            self.channels["webhook"] = WebhookNotificationChannel({
                "url": self.settings.notifications.webhook_url
            })
            self.logger.info("Webhook notification channel initialized")
        
        # Initialize Slack channel
        if self.settings.notifications.slack_webhook_url:
            self.channels["slack"] = SlackNotificationChannel({
                "webhook_url": self.settings.notifications.slack_webhook_url
            })
            self.logger.info("Slack notification channel initialized")
        
        if not self.channels:
            self.logger.warning("No notification channels configured")
    
    def add_channel(self, name: str, channel: BaseNotificationChannel) -> None:
        """Add a custom notification channel."""
        if not channel.validate_config():
            raise ValueError(f"Invalid configuration for channel: {name}")
        
        self.channels[name] = channel
        self.logger.info("Notification channel added", channel_name=name, channel_type=channel.get_channel_type().value)
    
    def send_notification(
        self,
        subject: str,
        message: str,
        payload: Dict[str, Any],
        channels: Optional[List[str]] = None,
        task_execution_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Send notification through specified channels."""
        if not self.settings.notifications.enable_notifications:
            self.logger.debug("Notifications disabled, skipping")
            return {"sent": 0, "failed": 0, "channels": []}
        
        # Use all channels if none specified
        if channels is None:
            channels = list(self.channels.keys())
        
        results = {
            "sent": 0,
            "failed": 0,
            "channels": []
        }
        
        for channel_name in channels:
            if channel_name not in self.channels:
                self.logger.warning("Unknown notification channel", channel=channel_name)
                continue
            
            channel = self.channels[channel_name]
            
            # Create notification log entry
            notification_log = NotificationLog(
                task_execution_id=task_execution_id,
                notification_type=channel.get_channel_type().value,
                recipient=self._get_channel_recipient(channel),
                subject=subject,
                message=message,
                payload=payload,
                status=NotificationStatus.PENDING.value
            )
            
            self.db.add(notification_log)
            self.db.commit()
            self.db.refresh(notification_log)
            
            try:
                # Send notification
                response = channel.send(subject, message, payload)
                
                # Update log with success
                notification_log.status = NotificationStatus.SENT.value
                notification_log.response_code = response.get("status_code")
                notification_log.response_message = response.get("response_text", "")[:1000]
                
                self.db.commit()
                
                results["sent"] += 1
                results["channels"].append({
                    "name": channel_name,
                    "type": channel.get_channel_type().value,
                    "status": "sent",
                    "response_code": response.get("status_code")
                })
                
                self.logger.info(
                    "Notification sent successfully",
                    channel=channel_name,
                    notification_id=notification_log.id
                )
                
            except Exception as e:
                # Update log with failure
                notification_log.status = NotificationStatus.FAILED.value
                notification_log.response_message = str(e)[:1000]
                
                self.db.commit()
                
                results["failed"] += 1
                results["channels"].append({
                    "name": channel_name,
                    "type": channel.get_channel_type().value,
                    "status": "failed",
                    "error": str(e)
                })
                
                self.log_error(e, {
                    "channel": channel_name,
                    "notification_id": notification_log.id
                })
        
        self.logger.info(
            "Notification batch completed",
            sent=results["sent"],
            failed=results["failed"],
            total_channels=len(channels)
        )
        
        return results
    
    def _get_channel_recipient(self, channel: BaseNotificationChannel) -> str:
        """Get recipient identifier for a channel."""
        if channel.get_channel_type() == NotificationType.WEBHOOK:
            return channel.config.get("url", "unknown")
        elif channel.get_channel_type() == NotificationType.SLACK:
            return channel.config.get("webhook_url", "unknown")
        elif channel.get_channel_type() == NotificationType.EMAIL:
            return channel.config.get("to_email", "unknown")
        else:
            return "unknown"
    
    def send_task_success_notification(self, task_execution: TaskExecution, result: Dict[str, Any]) -> None:
        """Send notification for successful task completion."""
        subject = f"✅ Task Completed: {task_execution.task_name}"
        
        # Build message based on task type
        if task_execution.task_type == "full_pipeline":
            message = self._build_pipeline_success_message(result)
        elif task_execution.task_type == "news_collection":
            message = self._build_collection_success_message(result)
        elif task_execution.task_type == "ai_processing":
            message = self._build_processing_success_message(result)
        else:
            message = f"Task '{task_execution.task_name}' completed successfully in {task_execution.duration_seconds:.1f} seconds."
        
        payload = {
            "task_execution_id": task_execution.id,
            "task_name": task_execution.task_name,
            "task_type": task_execution.task_type,
            "duration": task_execution.duration_seconds,
            "result": result
        }
        
        self.send_notification(subject, message, payload, task_execution_id=task_execution.id)
    
    def send_task_failure_notification(self, task_execution: TaskExecution) -> None:
        """Send notification for failed task execution."""
        subject = f"❌ Task Failed: {task_execution.task_name}"
        message = f"Task '{task_execution.task_name}' failed after {task_execution.duration_seconds:.1f} seconds.\n\nError: {task_execution.error_message}"
        
        payload = {
            "task_execution_id": task_execution.id,
            "task_name": task_execution.task_name,
            "task_type": task_execution.task_type,
            "duration": task_execution.duration_seconds,
            "error": task_execution.error_message,
            "error_details": task_execution.error_details
        }
        
        self.send_notification(subject, message, payload, task_execution_id=task_execution.id)
    
    def _build_pipeline_success_message(self, result: Dict[str, Any]) -> str:
        """Build success message for full pipeline execution."""
        collection = result.get("collection", {})
        processing = result.get("processing", {})
        summary = result.get("summary", {})
        
        message_parts = [
            f"Daily carbon regulation news pipeline completed successfully in {result.get('total_duration', 0):.1f} seconds.",
            "",
            f"📰 Collection: {collection.get('saved_count', 0)} new articles from {len(collection.get('sources', []))} sources",
            f"🤖 Processing: {processing.get('processed_count', 0)} articles analyzed with AI",
        ]
        
        if summary.get("statistics", {}).get("total_articles", 0) > 0:
            message_parts.append(f"📊 Summary: Generated for {summary['statistics']['total_articles']} articles")
        
        return "\n".join(message_parts)
    
    def _build_collection_success_message(self, result: Dict[str, Any]) -> str:
        """Build success message for news collection."""
        return f"News collection completed: {result.get('saved_count', 0)} new articles saved from {len(result.get('sources', []))} sources."
    
    def _build_processing_success_message(self, result: Dict[str, Any]) -> str:
        """Build success message for AI processing."""
        return f"AI processing completed: {result.get('processed_count', 0)} articles processed out of {result.get('total_unprocessed', 0)} unprocessed articles."
    
    def get_notification_history(self, limit: int = 50) -> List[NotificationLog]:
        """Get recent notification history."""
        return self.db.query(NotificationLog).order_by(
            NotificationLog.sent_at.desc()
        ).limit(limit).all()
    
    def retry_failed_notifications(self, max_age_hours: int = 24) -> Dict[str, Any]:
        """Retry failed notifications within the specified age."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=max_age_hours)
        
        failed_notifications = self.db.query(NotificationLog).filter(
            NotificationLog.status == NotificationStatus.FAILED.value,
            NotificationLog.sent_at >= cutoff_time,
            NotificationLog.retry_count < NotificationLog.max_retries
        ).all()
        
        retry_results = {"retried": 0, "succeeded": 0, "failed": 0}
        
        for notification in failed_notifications:
            try:
                # Find the appropriate channel
                channel = None
                for ch in self.channels.values():
                    if ch.get_channel_type().value == notification.notification_type:
                        channel = ch
                        break
                
                if not channel:
                    continue
                
                # Retry sending
                response = channel.send(
                    notification.subject,
                    notification.message,
                    notification.payload or {}
                )
                
                # Update notification log
                notification.retry_count += 1
                notification.status = NotificationStatus.SENT.value
                notification.response_code = response.get("status_code")
                notification.response_message = response.get("response_text", "")[:1000]
                
                retry_results["retried"] += 1
                retry_results["succeeded"] += 1
                
            except Exception as e:
                notification.retry_count += 1
                notification.response_message = str(e)[:1000]
                
                retry_results["retried"] += 1
                retry_results["failed"] += 1
                
                self.log_error(e, {"notification_id": notification.id})
            
            self.db.commit()
        
        self.logger.info("Notification retry completed", **retry_results)
        return retry_results
    
    def __del__(self):
        """Clean up database session."""
        if hasattr(self, 'db') and self.db:
            self.db.close()
