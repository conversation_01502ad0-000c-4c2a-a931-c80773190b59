"""
Monkey patch for feedparser to fix deprecation warnings.

This module patches the feedparser library to fix the deprecation warning:
"'count' is passed as positional argument" in re.sub() calls.

The issue is in feedparser/html.py line 152 where re.sub() is called as:
re.sub(pattern, repl, string, flags) instead of re.sub(pattern, repl, string, count=0, flags=flags)
"""

import re
import warnings


def apply_feedparser_patches():
    """
    Apply monkey patches to fix feedparser deprecation warnings.

    This function should be called before importing feedparser.
    """
    try:
        # Store the original re.sub function
        original_re_sub = re.sub

        def fixed_re_sub(pattern, repl, string, count=0, flags=0):
            """
            Fixed re.sub that handles the feedparser calling convention.

            feedparser calls: re.sub(pattern, repl, string, re.IGNORECASE)
            which in Python 3.13+ is interpreted as: re.sub(pattern, repl, string, count=re.IGNORECASE)

            We need to detect this case and fix it.
            """
            # Check if count is actually a flags value (common feedparser case)
            if count in (re.IGNORECASE, re.MULTILINE, re.DOTALL, re.VERBOSE, re.ASCII, re.LOCALE, re.UNICODE):
                # count is actually flags, so fix the call
                return original_re_sub(pattern, repl, string, count=0, flags=count)
            else:
                # Normal call
                return original_re_sub(pattern, repl, string, count=count, flags=flags)

        # Replace the global re.sub function
        re.sub = fixed_re_sub

    except Exception as e:
        # Don't break the application if patching fails
        warnings.warn(f"Failed to apply feedparser patches: {e}", RuntimeWarning)


def restore_original_re_sub():
    """
    Restore the original re.sub function.
    
    This can be used to undo the monkey patch if needed.
    """
    import importlib
    import re as re_module
    
    # Reload the re module to get the original function
    importlib.reload(re_module)
    
    try:
        import feedparser.html
        feedparser.html.re = re_module
    except ImportError:
        pass
