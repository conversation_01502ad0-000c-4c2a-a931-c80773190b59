"""
Health check and system status API routes.
"""

import time
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ...core.database import get_db, DatabaseManager
from ...core.models import NewsArticle, TaskExecution
from ...core.logging import get_logger
from ..schemas import HealthCheckResponse, StatisticsResponse, SystemStatistics, TaskStatistics, NewsStatistics

logger = get_logger(__name__)
router = APIRouter(prefix="/health", tags=["Health"])

# Track application start time
_app_start_time = time.time()


@router.get("/", response_model=HealthCheckResponse)
async def health_check(db: Session = Depends(get_db)):
    """
    Perform a comprehensive health check of the system.
    
    Returns system status including database connectivity, scheduler status,
    and basic statistics.
    """
    try:
        # Check database connectivity
        database_connected = DatabaseManager.health_check()
        
        # Get basic statistics
        total_articles = db.query(NewsArticle).count() if database_connected else 0
        
        # Get last task execution
        last_task = None
        if database_connected:
            last_task_execution = db.query(TaskExecution).order_by(
                TaskExecution.started_at.desc()
            ).first()
            last_task = last_task_execution.started_at if last_task_execution else None
        
        # Calculate uptime
        uptime_seconds = time.time() - _app_start_time
        
        # Determine overall status
        status = "healthy" if database_connected else "unhealthy"
        
        return HealthCheckResponse(
            success=True,
            message=f"System is {status}",
            status=status,
            database_connected=database_connected,
            scheduler_running=False,  # Will be updated when scheduler is integrated
            total_articles=total_articles,
            last_task_execution=last_task,
            uptime_seconds=uptime_seconds
        )
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Health check failed: {str(e)}"
        )


@router.get("/statistics", response_model=StatisticsResponse)
async def get_system_statistics(db: Session = Depends(get_db)):
    """
    Get comprehensive system statistics including task execution and news collection metrics.
    """
    try:
        # Task statistics
        total_executions = db.query(TaskExecution).count()
        successful_executions = db.query(TaskExecution).filter(
            TaskExecution.status == "success"
        ).count()
        failed_executions = db.query(TaskExecution).filter(
            TaskExecution.status == "failed"
        ).count()
        
        success_rate = successful_executions / total_executions if total_executions > 0 else 0.0
        
        # Get last execution timestamps
        last_execution_record = db.query(TaskExecution).order_by(
            TaskExecution.started_at.desc()
        ).first()
        last_execution = last_execution_record.started_at if last_execution_record else None
        
        last_success_record = db.query(TaskExecution).filter(
            TaskExecution.status == "success"
        ).order_by(TaskExecution.started_at.desc()).first()
        last_success = last_success_record.started_at if last_success_record else None
        
        task_stats = TaskStatistics(
            total_executions=total_executions,
            successful_executions=successful_executions,
            failed_executions=failed_executions,
            success_rate=success_rate,
            last_execution=last_execution,
            last_success=last_success,
            scheduler_running=False  # Will be updated when scheduler is integrated
        )
        
        # News statistics
        total_articles = db.query(NewsArticle).count()
        processed_articles = db.query(NewsArticle).filter(
            NewsArticle.is_processed == True
        ).count()
        unprocessed_articles = total_articles - processed_articles
        
        # Articles collected today
        today = datetime.now(timezone.utc).date()
        articles_today = db.query(NewsArticle).filter(
            NewsArticle.collected_at >= today
        ).count()
        
        # Count unique sources
        sources_count = db.query(NewsArticle.source_name).distinct().count()
        
        # Get category and type distributions
        categories = {}
        types = {}
        
        processed_articles_with_classification = db.query(NewsArticle).filter(
            NewsArticle.is_processed == True,
            NewsArticle.ai_classification.isnot(None)
        ).all()
        
        for article in processed_articles_with_classification:
            if article.ai_classification:
                # Count categories
                category = article.ai_classification.get("category", "Unknown")
                categories[category] = categories.get(category, 0) + 1
                
                # Count types
                article_type = article.ai_classification.get("type", "Unknown")
                types[article_type] = types.get(article_type, 0) + 1
        
        news_stats = NewsStatistics(
            total_articles=total_articles,
            processed_articles=processed_articles,
            unprocessed_articles=unprocessed_articles,
            articles_today=articles_today,
            sources_count=sources_count,
            categories=categories,
            types=types
        )
        
        # System statistics
        database_health = DatabaseManager.health_check()
        
        system_stats = SystemStatistics(
            task_stats=task_stats,
            news_stats=news_stats,
            database_health=database_health,
            last_updated=datetime.now(timezone.utc)
        )
        
        return StatisticsResponse(
            success=True,
            message="Statistics retrieved successfully",
            statistics=system_stats
        )
        
    except Exception as e:
        logger.error("Failed to get system statistics", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get statistics: {str(e)}"
        )


@router.get("/database")
async def check_database_health():
    """
    Check database connectivity and basic functionality.
    """
    try:
        is_healthy = DatabaseManager.health_check()
        
        if is_healthy:
            return {
                "success": True,
                "message": "Database is healthy and accessible",
                "status": "connected",
                "timestamp": datetime.now(timezone.utc)
            }
        else:
            raise HTTPException(
                status_code=503,
                detail="Database is not accessible"
            )
            
    except Exception as e:
        logger.error("Database health check failed", error=str(e))
        raise HTTPException(
            status_code=503,
            detail=f"Database health check failed: {str(e)}"
        )


@router.get("/scheduler")
async def check_scheduler_status():
    """
    Check scheduler status and recent task executions.
    """
    try:
        # This will be implemented when scheduler is integrated
        # For now, return basic information
        
        return {
            "success": True,
            "message": "Scheduler status retrieved",
            "scheduler_running": False,  # Placeholder
            "next_scheduled_run": None,  # Placeholder
            "last_execution": None,  # Placeholder
            "timestamp": datetime.now(timezone.utc)
        }
        
    except Exception as e:
        logger.error("Scheduler status check failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Scheduler status check failed: {str(e)}"
        )
