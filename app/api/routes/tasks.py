"""
Task management API routes for viewing and triggering tasks.
"""

from datetime import datetime, timezone
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc

from ...core.database import get_db
from ...core.models import TaskExecution, TaskResult
from ...core.logging import get_logger
from ...services.scheduler import TaskSchedulerService, TaskType
from ..schemas import (
    TaskExecutionResponse, TaskExecutionListResponse, TaskResultResponse, 
    TaskResultListResponse, ManualTaskTriggerRequest, ManualTaskTriggerResponse,
    BaseResponse, ErrorResponse
)

logger = get_logger(__name__)
router = APIRouter(prefix="/tasks", tags=["Tasks"])


@router.get("/executions", response_model=TaskExecutionListResponse)
async def get_task_executions(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Number of items per page"),
    status: Optional[str] = Query(None, description="Filter by task status"),
    task_type: Optional[str] = Query(None, description="Filter by task type"),
    db: Session = Depends(get_db)
):
    """
    Get a paginated list of task executions with optional filtering.
    
    - **page**: Page number (starts from 1)
    - **page_size**: Number of items per page (max 100)
    - **status**: Filter by task status (pending, running, success, failed)
    - **task_type**: Filter by task type (news_collection, ai_processing, daily_summary, full_pipeline)
    """
    try:
        # Build query
        query = db.query(TaskExecution)
        
        # Apply filters
        if status:
            query = query.filter(TaskExecution.status == status)
        if task_type:
            query = query.filter(TaskExecution.task_type == task_type)
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination and ordering
        executions = query.order_by(desc(TaskExecution.started_at)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()
        
        # Convert to response models
        execution_responses = [
            TaskExecutionResponse.model_validate(execution) 
            for execution in executions
        ]
        
        return TaskExecutionListResponse(
            success=True,
            message=f"Retrieved {len(execution_responses)} task executions",
            executions=execution_responses,
            total_count=total_count,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        logger.error("Failed to get task executions", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get task executions: {str(e)}"
        )


@router.get("/executions/{execution_id}", response_model=TaskExecutionResponse)
async def get_task_execution(execution_id: int, db: Session = Depends(get_db)):
    """
    Get details of a specific task execution by ID.
    """
    try:
        execution = db.query(TaskExecution).filter(
            TaskExecution.id == execution_id
        ).first()
        
        if not execution:
            raise HTTPException(
                status_code=404,
                detail=f"Task execution with ID {execution_id} not found"
            )
        
        return TaskExecutionResponse.model_validate(execution)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get task execution", execution_id=execution_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get task execution: {str(e)}"
        )


@router.get("/results", response_model=TaskResultListResponse)
async def get_task_results(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Number of items per page"),
    result_type: Optional[str] = Query(None, description="Filter by result type"),
    execution_id: Optional[int] = Query(None, description="Filter by task execution ID"),
    db: Session = Depends(get_db)
):
    """
    Get a paginated list of task results with optional filtering.
    
    - **page**: Page number (starts from 1)
    - **page_size**: Number of items per page (max 100)
    - **result_type**: Filter by result type (daily_summary, news_collection, ai_processing)
    - **execution_id**: Filter by specific task execution ID
    """
    try:
        # Build query
        query = db.query(TaskResult)
        
        # Apply filters
        if result_type:
            query = query.filter(TaskResult.result_type == result_type)
        if execution_id:
            query = query.filter(TaskResult.task_execution_id == execution_id)
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination and ordering
        results = query.order_by(desc(TaskResult.created_at)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()
        
        # Convert to response models
        result_responses = [
            TaskResultResponse.model_validate(result) 
            for result in results
        ]
        
        return TaskResultListResponse(
            success=True,
            message=f"Retrieved {len(result_responses)} task results",
            results=result_responses,
            total_count=total_count,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        logger.error("Failed to get task results", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get task results: {str(e)}"
        )


@router.get("/results/{result_id}", response_model=TaskResultResponse)
async def get_task_result(result_id: int, db: Session = Depends(get_db)):
    """
    Get details of a specific task result by ID.
    """
    try:
        result = db.query(TaskResult).filter(TaskResult.id == result_id).first()
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"Task result with ID {result_id} not found"
            )
        
        return TaskResultResponse.model_validate(result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get task result", result_id=result_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get task result: {str(e)}"
        )


@router.post("/trigger", response_model=ManualTaskTriggerResponse)
async def trigger_manual_task(
    request: ManualTaskTriggerRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Manually trigger a task execution.
    
    This endpoint allows you to manually start any type of task:
    - **news_collection**: Collect news from configured sources
    - **ai_processing**: Process unprocessed articles with AI
    - **daily_summary**: Generate daily summary from processed articles
    - **full_pipeline**: Run the complete pipeline (collection -> processing -> summary)
    
    The task will be executed in the background and you can monitor its progress
    using the task execution endpoints.
    """
    try:
        # Create task scheduler service
        scheduler_service = TaskSchedulerService(db)
        
        # Generate task name if not provided
        task_name = request.task_name or f"manual_{request.task_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Add task to background execution
        def execute_task():
            try:
                scheduler_service.execute_task(request.task_type, task_name)
            except Exception as e:
                logger.error("Background task execution failed", task_name=task_name, error=str(e))
        
        background_tasks.add_task(execute_task)
        
        # Create a pending task execution record to return immediately
        task_execution = TaskExecution(
            task_name=task_name,
            task_type=request.task_type.value,
            status="pending",
            started_at=datetime.now(timezone.utc)
        )
        
        db.add(task_execution)
        db.commit()
        db.refresh(task_execution)
        
        logger.info("Manual task triggered", task_name=task_name, task_type=request.task_type.value)
        
        return ManualTaskTriggerResponse(
            success=True,
            message=f"Task '{task_name}' has been triggered and is running in the background",
            task_execution=TaskExecutionResponse.model_validate(task_execution)
        )
        
    except Exception as e:
        logger.error("Failed to trigger manual task", task_type=request.task_type.value, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to trigger task: {str(e)}"
        )


@router.get("/types")
async def get_available_task_types():
    """
    Get list of available task types that can be triggered manually.
    """
    return {
        "success": True,
        "message": "Available task types retrieved",
        "task_types": [
            {
                "value": TaskType.NEWS_COLLECTION.value,
                "name": "News Collection",
                "description": "Collect news articles from configured sources"
            },
            {
                "value": TaskType.AI_PROCESSING.value,
                "name": "AI Processing",
                "description": "Process unprocessed articles with AI analysis"
            },
            {
                "value": TaskType.DAILY_SUMMARY.value,
                "name": "Daily Summary",
                "description": "Generate daily summary from processed articles"
            },
            {
                "value": TaskType.FULL_PIPELINE.value,
                "name": "Full Pipeline",
                "description": "Run complete pipeline: collection -> processing -> summary"
            }
        ]
    }


@router.get("/recent")
async def get_recent_task_activity(
    limit: int = Query(10, ge=1, le=50, description="Number of recent activities to return"),
    db: Session = Depends(get_db)
):
    """
    Get recent task activity including both executions and results.
    """
    try:
        # Get recent executions
        recent_executions = db.query(TaskExecution).order_by(
            desc(TaskExecution.started_at)
        ).limit(limit).all()
        
        # Get recent results
        recent_results = db.query(TaskResult).order_by(
            desc(TaskResult.created_at)
        ).limit(limit).all()
        
        return {
            "success": True,
            "message": f"Retrieved recent task activity",
            "recent_executions": [
                {
                    "id": execution.id,
                    "task_name": execution.task_name,
                    "task_type": execution.task_type,
                    "status": execution.status,
                    "started_at": execution.started_at,
                    "duration_seconds": execution.duration_seconds
                }
                for execution in recent_executions
            ],
            "recent_results": [
                {
                    "id": result.id,
                    "result_type": result.result_type,
                    "title": result.title,
                    "created_at": result.created_at,
                    "task_execution_id": result.task_execution_id
                }
                for result in recent_results
            ]
        }
        
    except Exception as e:
        logger.error("Failed to get recent task activity", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get recent activity: {str(e)}"
        )
