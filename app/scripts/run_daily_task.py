"""
Standalone script for running daily news collection and processing tasks.
This can be used for manual execution or cron jobs.
"""

import sys
import os
import argparse
from datetime import datetime, timezone
from pathlib import Path

# Add the project root directory to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.core.config import get_settings
from app.core.logging import setup_logging, get_logger
from app.core.database import DatabaseManager
from app.services.scheduler import TaskSchedulerService, TaskType


def main():
    """Main function for the daily task script."""
    parser = argparse.ArgumentParser(description="Run daily carbon regulation news tasks")
    parser.add_argument(
        "--task-type",
        choices=["collection", "processing", "summary", "full-pipeline"],
        default="full-pipeline",
        help="Type of task to run (default: full-pipeline)"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without actually executing"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    logger = get_logger(__name__)
    
    if args.verbose:
        logger.info("Verbose logging enabled")
    
    try:
        # Initialize database
        logger.info("Initializing database")
        DatabaseManager.initialize()
        
        # Map task type argument to enum
        task_type_map = {
            "collection": TaskType.NEWS_COLLECTION,
            "processing": TaskType.AI_PROCESSING,
            "summary": TaskType.DAILY_SUMMARY,
            "full-pipeline": TaskType.FULL_PIPELINE
        }
        
        task_type = task_type_map[args.task_type]
        task_name = f"script_{args.task_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if args.dry_run:
            logger.info(
                "DRY RUN: Would execute task",
                task_type=task_type.value,
                task_name=task_name
            )
            print(f"DRY RUN: Would execute {task_type.value} task named '{task_name}'")
            return 0
        
        # Create scheduler service and execute task
        logger.info("Starting task execution", task_type=task_type.value, task_name=task_name)
        
        scheduler_service = TaskSchedulerService()
        task_execution = scheduler_service.execute_task(task_type, task_name)
        
        # Print results
        if task_execution.status == "success":
            logger.info(
                "Task completed successfully",
                task_id=task_execution.id,
                duration=task_execution.duration_seconds
            )
            print(f"Task completed successfully in {task_execution.duration_seconds:.1f} seconds")
            
            if task_execution.result_summary:
                print("\nResults:")
                if args.task_type == "full-pipeline":
                    result = task_execution.result_summary
                    print(f"  Collection: {result.get('collection', {}).get('saved_count', 0)} new articles")
                    print(f"  Processing: {result.get('processing', {}).get('processed_count', 0)} articles analyzed")
                    if result.get('summary', {}).get('statistics', {}).get('total_articles', 0) > 0:
                        print(f"  Summary: Generated for {result['summary']['statistics']['total_articles']} articles")
                elif args.task_type == "collection":
                    result = task_execution.result_summary
                    print(f"  Collected: {result.get('saved_count', 0)} new articles from {len(result.get('sources', []))} sources")
                elif args.task_type == "processing":
                    result = task_execution.result_summary
                    print(f"  Processed: {result.get('processed_count', 0)} articles out of {result.get('total_unprocessed', 0)} unprocessed")
                elif args.task_type == "summary":
                    result = task_execution.result_summary
                    print(f"  Summary: Generated for {result.get('statistics', {}).get('total_articles', 0)} articles")
            
            return 0
        else:
            logger.error(
                "Task failed",
                task_id=task_execution.id,
                error=task_execution.error_message
            )
            print(f"Task failed: {task_execution.error_message}")
            return 1
            
    except Exception as e:
        logger.error("Script execution failed", error=str(e))
        print(f"Script failed: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
