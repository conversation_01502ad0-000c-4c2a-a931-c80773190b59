"""
Application configuration management.
"""

import os
import yaml
from pathlib import Path
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from pydantic_settings import BaseSettings


class DatabaseConfig(BaseModel):
    """Database configuration."""
    url: str = Field(default="sqlite:///./carbon_news.db", description="Database URL")
    echo: bool = Field(default=False, description="Enable SQL query logging")


class APIConfig(BaseModel):
    """API server configuration."""
    host: str = Field(default="0.0.0.0", description="API host")
    port: int = Field(default=8000, description="API port")
    debug: bool = Field(default=False, description="Debug mode")


class SearchSettingsConfig(BaseModel):
    """Advanced search settings configuration."""
    include_domains: List[str] = Field(default=[], description="Domains to include in search")
    exclude_domains: List[str] = Field(
        default=["twitter.com", "facebook.com", "instagram.com", "tiktok.com"],
        description="Domains to exclude from search"
    )
    search_depth: str = Field(default="basic", description="Search depth: basic, advanced")
    extract_full_content: bool = Field(default=True, description="Extract full content from articles")
    min_content_length: int = Field(default=100, description="Minimum content length in characters")


class NewsCollectorConfig(BaseModel):
    """News collection configuration."""
    max_articles_per_source: int = Field(default=10, description="Maximum articles per source")
    default_time_range: str = Field(default="day", description="Default time range for news search")

    # Search queries for carbon regulation news
    search_queries: List[str] = Field(
        default=[
            "carbon regulations emission standards environmental policy",
            "clean energy regulations carbon policy sustainability",
            "carbon accounting standards disclosure reporting",
            "carbon pricing markets ETS carbon tax",
            "climate regulations environmental compliance"
        ],
        description="Search queries for news collection"
    )

    # Specific news sources to monitor
    specific_sources: List[str] = Field(
        default=[
            "https://www.reuters.com/sustainability/clean-energy/",
            "https://www.reuters.com/sustainability/climate-energy/",
        ],
        description="Specific news source URLs to monitor"
    )

    # RSS feeds to monitor
    rss_feeds: List[str] = Field(
        default=[
            "https://www.carbonbrief.org/feed/",
            "https://feeds.feedburner.com/EnvironmentalDefenseFund",
        ],
        description="RSS feed URLs to monitor for carbon regulation and environmental news"
    )

    # Advanced search settings
    search_settings: SearchSettingsConfig = Field(default_factory=SearchSettingsConfig)


class RetrySettingsConfig(BaseModel):
    """Retry settings for failed tasks."""
    max_retries: int = Field(default=3, description="Maximum number of retries")
    retry_delay_minutes: int = Field(default=5, description="Delay between retries in minutes")


class SchedulerConfig(BaseModel):
    """Task scheduler configuration."""
    daily_run_time: str = Field(default="09:00", description="Daily task run time (HH:MM)")
    max_task_history: int = Field(default=100, description="Maximum task results to keep")
    task_timeout_minutes: int = Field(default=30, description="Task execution timeout")
    enable_auto_scheduling: bool = Field(default=True, description="Enable automatic scheduling")
    retry_settings: RetrySettingsConfig = Field(default_factory=RetrySettingsConfig)


class EmailConfig(BaseModel):
    """Email notification configuration."""
    enabled: bool = Field(default=False, description="Enable email notifications")
    smtp_server: Optional[str] = Field(default=None, description="SMTP server address")
    smtp_port: int = Field(default=587, description="SMTP server port")
    username: Optional[str] = Field(default=None, description="SMTP username")
    password: Optional[str] = Field(default=None, description="SMTP password")
    from_address: Optional[str] = Field(default=None, description="From email address")
    to_addresses: List[str] = Field(default=[], description="List of recipient email addresses")


class NotificationTriggersConfig(BaseModel):
    """Notification trigger configuration."""
    on_collection_complete: bool = Field(default=True, description="Notify when collection completes")
    on_error: bool = Field(default=True, description="Notify when errors occur")
    on_high_priority_articles: bool = Field(default=False, description="Notify for high-priority articles")
    min_articles_threshold: int = Field(default=5, description="Minimum articles to trigger notification")


class NotificationConfig(BaseModel):
    """Notification system configuration."""
    webhook_url: Optional[str] = Field(default=None, description="Webhook URL for notifications")
    slack_webhook_url: Optional[str] = Field(default=None, description="Slack webhook URL")
    enable_notifications: bool = Field(default=True, description="Enable notifications")


class AIModelConfig(BaseModel):
    """AI model configuration."""
    model_name: str = Field(default="openai/gpt-4o-mini", description="Model name")
    temperature: float = Field(default=0.1, description="Model temperature")
    max_tokens: int = Field(default=2000, description="Maximum tokens")


class AIModelsConfig(BaseModel):
    """AI models configuration."""
    news_parser: AIModelConfig = Field(default_factory=AIModelConfig)
    url_extractor: AIModelConfig = Field(default_factory=lambda: AIModelConfig(temperature=0.0, max_tokens=1000))
    classifier: AIModelConfig = Field(default_factory=lambda: AIModelConfig(max_tokens=1500))
    summarizer: AIModelConfig = Field(default_factory=lambda: AIModelConfig(temperature=0.2, max_tokens=3000))


class AIProcessingConfig(BaseModel):
    """AI processing configuration."""
    models: AIModelsConfig = Field(default_factory=AIModelsConfig)
    analysis_settings: Dict[str, Any] = Field(default_factory=lambda: {
        "enable_sentiment_analysis": True,
        "enable_topic_classification": True,
        "enable_summary_generation": True,
        "min_confidence_score": 0.7
    })


class AIParserPromptsConfig(BaseModel):
    """AI parser prompts configuration."""
    classification_system_prompt: str = Field(default="You are an expert at classifying carbon regulation and climate policy news.")
    content_extraction_system_prompt: str = Field(default="You are an expert at extracting and summarizing news content.")
    summary_generation_system_prompt: str = Field(default="You are an expert at creating executive summaries.")
    article_clustering_system_prompt: str = Field(default="You are an expert at analyzing and clustering news articles.")
    enhanced_summary_system_prompt: str = Field(default="You are an expert at creating comprehensive summaries in markdown format.")

    classification_prompt_template: str = Field(default="Classify this article: {content}")
    content_extraction_prompt_template: str = Field(default="Extract content from: {content}")
    daily_summary_prompt_template: str = Field(default="Create summary from: {article_summaries}")
    article_clustering_prompt_template: str = Field(default="Cluster these articles: {article_summaries}")
    enhanced_summary_prompt_template: str = Field(default="Create enhanced summary: {clustered_articles}")


class NewsCollectorPromptsConfig(BaseModel):
    """News collector prompts configuration."""
    url_extractor_system_prompt: str = Field(default="You are an expert at extracting article URLs.")
    url_extraction_prompt_template: str = Field(default="Extract URLs from: {page_content}")


class PromptsConfig(BaseModel):
    """Prompts configuration."""
    ai_parser: AIParserPromptsConfig = Field(default_factory=AIParserPromptsConfig)
    news_collector: NewsCollectorPromptsConfig = Field(default_factory=NewsCollectorPromptsConfig)


class ModelConfig(BaseModel):
    """Individual AI model configuration."""
    model_name: str = Field(description="OpenRouter model name")
    temperature: float = Field(default=0.1, description="Temperature for AI responses")
    max_tokens: int = Field(default=2000, description="Maximum tokens for AI responses")


class ModelsConfig(BaseModel):
    """Configuration for different AI models used by various services."""
    news_parser: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            model_name="openai/gpt-4.1-mini",
            temperature=0.1,
            max_tokens=2000
        ),
        description="Model for news parsing and content analysis"
    )
    url_extractor: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            model_name="openai/gpt-4.1-mini",
            temperature=0.0,
            max_tokens=1000
        ),
        description="Model for URL extraction from web pages"
    )
    classifier: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            model_name="openai/gpt-4.1-mini",
            temperature=0.1,
            max_tokens=1500
        ),
        description="Model for content classification"
    )
    summarizer: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            model_name="openai/gpt-4.1-mini",
            temperature=0.2,
            max_tokens=3000
        ),
        description="Model for summary generation"
    )


class AnalysisSettingsConfig(BaseModel):
    """Content analysis settings configuration."""
    enable_sentiment_analysis: bool = Field(default=True, description="Enable sentiment analysis")
    enable_topic_classification: bool = Field(default=True, description="Enable topic classification")
    enable_summary_generation: bool = Field(default=True, description="Enable summary generation")
    min_confidence_score: float = Field(default=0.7, description="Minimum confidence score")


class AIProcessingConfig(BaseModel):
    """AI processing configuration."""
    models: ModelsConfig = Field(default_factory=ModelsConfig)
    analysis_settings: AnalysisSettingsConfig = Field(default_factory=AnalysisSettingsConfig)








class NewsCollectorPromptsConfig(BaseModel):
    """News Collector service prompts configuration."""
    url_extractor_system_prompt: str = Field(
        default="""You are an expert at extracting article URLs from web page content.

Your task is to:
1. Identify links that point to news articles (not navigation, ads, or other pages)
2. Filter for articles that appear to be recent and relevant to carbon regulation, climate policy, or environmental news
3. Return only the URLs that look like individual news articles

Look for patterns like:
- URLs containing dates or article IDs
- Links with article-like titles related to carbon, climate, energy, or environmental policy
- Content that appears to be news articles rather than category pages

Return only the URLs in the specified format.""",
        description="System prompt for URL extractor agent"
    )

    url_extraction_prompt_template: str = Field(
        default="""Extract article URLs from this web page content that appear to be news articles about carbon regulation, climate policy, or environmental news.

Base URL: {base_url}

Page content:
{page_content}

Look for links that:
1. Point to individual news articles
2. Appear to be recent and relevant to carbon/climate/environmental topics
3. Are not navigation links, category pages, or advertisements

Return the complete URLs. If URLs are relative, they should be resolved against the base URL.""",
        description="Template for URL extraction prompts"
    )


class PromptsConfig(BaseModel):
    """Configuration for all AI prompts used by services."""
    ai_parser: AIParserPromptsConfig = Field(default_factory=AIParserPromptsConfig)
    news_collector: NewsCollectorPromptsConfig = Field(default_factory=NewsCollectorPromptsConfig)


def load_yaml_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """Load configuration from YAML file."""
    config_file = Path(config_path)
    if not config_file.exists():
        return {}

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    except Exception as e:
        print(f"Warning: Could not load YAML config from {config_path}: {e}")
        return {}


class HealthCheckConfig(BaseModel):
    """Health check configuration."""
    enabled: bool = Field(default=True, description="Enable health check endpoints")
    check_interval_minutes: int = Field(default=5, description="Health check interval in minutes")


class PerformanceConfig(BaseModel):
    """Performance monitoring configuration."""
    enabled: bool = Field(default=True, description="Enable performance metrics collection")
    retention_days: int = Field(default=30, description="Metrics retention period in days")


class AlertsConfig(BaseModel):
    """Alerting thresholds configuration."""
    max_collection_time_minutes: int = Field(default=60, description="Max collection time before alert")
    max_error_rate_percent: int = Field(default=10, description="Max error rate before alert")
    max_days_without_articles: int = Field(default=2, description="Max days without articles before alert")


class MonitoringConfig(BaseModel):
    """Monitoring and health check configuration."""
    health_check: HealthCheckConfig = Field(default_factory=HealthCheckConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    alerts: AlertsConfig = Field(default_factory=AlertsConfig)


class Settings(BaseSettings):
    """Main application settings."""

    # Environment
    environment: str = Field(default="development", description="Environment (development/production)")
    debug: bool = Field(default=True, description="Debug mode")
    log_level: str = Field(default="INFO", description="Logging level")

    # API Keys
    openrouter_api_key: Optional[str] = Field(default=None, description="OpenRouter API key")
    tavily_api_key: Optional[str] = Field(default=None, description="Tavily API key")

    # Component configurations
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    api: APIConfig = Field(default_factory=APIConfig)
    news_collector: NewsCollectorConfig = Field(default_factory=NewsCollectorConfig)
    scheduler: SchedulerConfig = Field(default_factory=SchedulerConfig)
    notifications: NotificationConfig = Field(default_factory=NotificationConfig)
    ai_processing: AIProcessingConfig = Field(default_factory=AIProcessingConfig)
    prompts: PromptsConfig = Field(default_factory=PromptsConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)

    def __init__(self, **kwargs):
        # Load YAML configuration first
        yaml_config = load_yaml_config()

        # Create a clean config dict with only valid Settings fields
        merged_config = {}

        # Copy valid fields from YAML config (excluding API keys for security)
        valid_fields = {
            'environment', 'debug', 'log_level',
            'database', 'api', 'news_collector', 'scheduler', 'notifications',
            'ai_processing', 'monitoring', 'prompts'
        }

        for key, value in yaml_config.items():
            if key in valid_fields:
                merged_config[key] = value

        # API keys are ONLY loaded from environment variables for security
        # They should never be stored in YAML files that might be committed to version control

        # Merge with kwargs, giving priority to kwargs
        merged_config.update(kwargs)

        super().__init__(**merged_config)

    model_config = ConfigDict(
        env_file=".env",
        env_nested_delimiter="__",
        case_sensitive=False
    )


# Global settings instance
_settings_instance = None


def get_settings() -> Settings:
    """Get application settings."""
    global _settings_instance
    if _settings_instance is None:
        _settings_instance = Settings()
    return _settings_instance


def reload_settings() -> Settings:
    """Reload settings from configuration files."""
    global _settings_instance
    _settings_instance = Settings()
    return _settings_instance


# Initialize settings on module import
settings = get_settings()
