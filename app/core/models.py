"""
SQLAlchemy models for the carbon regulation news application.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, ForeignKey, Float, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .database import Base

# Association table for many-to-many relationship between TaskResult and NewsArticle
task_result_articles = Table(
    'task_result_articles',
    Base.metadata,
    Column('task_result_id', Integer, ForeignKey('task_results.id'), primary_key=True),
    Column('news_article_id', Integer, ForeignKey('news_articles.id'), primary_key=True),
    Column('created_at', DateTime, default=func.now(), nullable=False)
)


class NewsArticle(Base):
    """Model for storing collected news articles."""
    
    __tablename__ = "news_articles"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(500), nullable=False, index=True)
    url = Column(String(1000), nullable=False, unique=True, index=True)
    content = Column(Text, nullable=False)
    source_name = Column(String(200), nullable=False, index=True)
    
    # Timestamps
    published_date = Column(DateTime, nullable=True)
    collected_at = Column(DateTime, default=func.now(), nullable=False)
    
    # AI processing status
    is_processed = Column(Boolean, default=False, nullable=False, index=True)
    processed_at = Column(DateTime, nullable=True)
    
    # AI-extracted content (JSON fields)
    ai_classification = Column(JSON, nullable=True)  # Category, type, jurisdictions, sectors
    ai_summary = Column(Text, nullable=True)
    ai_key_points = Column(JSON, nullable=True)  # List of key points
    ai_details = Column(JSON, nullable=True)  # Type-specific details
    
    # Relationships
    task_results = relationship("TaskResult", secondary=task_result_articles, back_populates="articles")
    
    def __repr__(self):
        return f"<NewsArticle(id={self.id}, title='{self.title[:50]}...', source='{self.source_name}')>"


class TaskExecution(Base):
    """Model for tracking task executions."""
    
    __tablename__ = "task_executions"
    
    id = Column(Integer, primary_key=True, index=True)
    task_name = Column(String(200), nullable=False, index=True)
    task_type = Column(String(100), nullable=False, index=True)  # 'news_collection', 'ai_processing', 'daily_summary'
    
    # Execution details
    status = Column(String(50), nullable=False, index=True)  # 'pending', 'running', 'success', 'failed'
    started_at = Column(DateTime, default=func.now(), nullable=False)
    completed_at = Column(DateTime, nullable=True)
    duration_seconds = Column(Float, nullable=True)
    
    # Results and errors
    result_summary = Column(JSON, nullable=True)  # Summary of what was accomplished
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # Configuration used for this execution
    config_snapshot = Column(JSON, nullable=True)
    
    # Relationships
    task_results = relationship("TaskResult", back_populates="task_execution")
    notification_logs = relationship("NotificationLog", back_populates="task_execution")
    
    def __repr__(self):
        return f"<TaskExecution(id={self.id}, name='{self.task_name}', status='{self.status}')>"


class TaskResult(Base):
    """Model for storing detailed task results and linking to processed articles."""
    
    __tablename__ = "task_results"
    
    id = Column(Integer, primary_key=True, index=True)
    task_execution_id = Column(Integer, ForeignKey("task_executions.id"), nullable=False, index=True)
    
    # Result details
    result_type = Column(String(100), nullable=False, index=True)  # 'daily_summary', 'article_analysis', etc.
    title = Column(String(500), nullable=False)
    summary = Column(Text, nullable=False)
    
    # Structured data
    key_findings = Column(JSON, nullable=True)  # List of key findings
    statistics = Column(JSON, nullable=True)  # Counts, metrics, etc.
    result_metadata = Column(JSON, nullable=True)  # Additional metadata
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # Relationships
    task_execution = relationship("TaskExecution", back_populates="task_results")
    
    # Many-to-many relationship with articles (a result can reference multiple articles)
    articles = relationship("NewsArticle", secondary=task_result_articles, back_populates="task_results")
    
    def __repr__(self):
        return f"<TaskResult(id={self.id}, type='{self.result_type}', title='{self.title[:50]}...')>"





class NotificationLog(Base):
    """Model for tracking sent notifications."""
    
    __tablename__ = "notification_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    task_execution_id = Column(Integer, ForeignKey("task_executions.id"), nullable=True, index=True)
    
    # Notification details
    notification_type = Column(String(100), nullable=False, index=True)  # 'webhook', 'slack', 'email'
    recipient = Column(String(500), nullable=False)  # URL, email, channel, etc.
    
    # Content
    subject = Column(String(500), nullable=True)
    message = Column(Text, nullable=False)
    payload = Column(JSON, nullable=True)  # Full payload sent
    
    # Status
    status = Column(String(50), nullable=False, index=True)  # 'sent', 'failed', 'pending'
    sent_at = Column(DateTime, default=func.now(), nullable=False)
    response_code = Column(Integer, nullable=True)
    response_message = Column(Text, nullable=True)
    
    # Retry information
    retry_count = Column(Integer, default=0, nullable=False)
    max_retries = Column(Integer, default=3, nullable=False)
    next_retry_at = Column(DateTime, nullable=True)
    
    # Relationships
    task_execution = relationship("TaskExecution", back_populates="notification_logs")
    
    def __repr__(self):
        return f"<NotificationLog(id={self.id}, type='{self.notification_type}', status='{self.status}')>"
