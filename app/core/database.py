"""
Database configuration and session management.
"""

from typing import Generator
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import declarative_base, sessionmaker, Session
from sqlalchemy.pool import StaticPool

from .config import get_settings
from .logging import get_logger

logger = get_logger(__name__)

# Create SQLAlchemy base class
Base = declarative_base()

# Naming convention for constraints (helps with migrations)
convention = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

Base.metadata = MetaData(naming_convention=convention)

# Global variables for engine and session factory
engine = None
SessionLocal = None


def create_database_engine():
    """Create and configure the database engine."""
    global engine
    
    settings = get_settings()
    
    # SQLite-specific configuration
    connect_args = {}
    if settings.database.url.startswith("sqlite"):
        connect_args = {
            "check_same_thread": False,  # Allow SQLite to be used across threads
        }
    
    engine = create_engine(
        settings.database.url,
        echo=settings.database.echo,
        connect_args=connect_args,
        poolclass=StaticPool if settings.database.url.startswith("sqlite") else None,
    )
    
    logger.info("Database engine created", database_url=settings.database.url)
    return engine


def create_session_factory():
    """Create the session factory."""
    global SessionLocal
    
    if engine is None:
        create_database_engine()
    
    SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine
    )
    
    logger.info("Database session factory created")
    return SessionLocal


def create_tables():
    """Create all database tables."""
    if engine is None:
        create_database_engine()
    
    # Import models to ensure they are registered with Base
    from .models import NewsArticle, TaskExecution, TaskResult, NotificationLog
    
    Base.metadata.create_all(bind=engine)
    logger.info("Database tables created")


def get_db() -> Generator[Session, None, None]:
    """
    Dependency function to get database session.
    Used with FastAPI's dependency injection.
    """
    if SessionLocal is None:
        create_session_factory()
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_db_session() -> Session:
    """
    Get a database session for use outside of FastAPI.
    Remember to close the session when done.
    """
    if SessionLocal is None:
        create_session_factory()
    
    return SessionLocal()


class DatabaseManager:
    """Database management utilities."""
    
    @staticmethod
    def initialize():
        """Initialize the database (create engine, tables, etc.)."""
        logger.info("Initializing database")
        create_database_engine()
        create_session_factory()
        create_tables()
        logger.info("Database initialization complete")
    
    @staticmethod
    def reset():
        """Reset the database (drop and recreate all tables)."""
        logger.warning("Resetting database - all data will be lost")
        
        if engine is None:
            create_database_engine()
        
        # Import models to ensure they are registered
        from .models import NewsArticle, TaskExecution, TaskResult, NotificationLog
        
        Base.metadata.drop_all(bind=engine)
        Base.metadata.create_all(bind=engine)
        
        logger.info("Database reset complete")
    
    @staticmethod
    def health_check() -> bool:
        """Check if database is accessible."""
        try:
            from sqlalchemy import text
            db = get_db_session()
            db.execute(text("SELECT 1"))
            db.close()
            return True
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False
