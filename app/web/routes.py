"""
Web UI routes for the Carbon Regulation News application.
"""

from datetime import datetime, date
from typing import Optional
from fastapi import APIRouter, Depends, Request, HTTPException, Query
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_

from ..core.database import get_db
from ..core.models import NewsArticle, TaskExecution, TaskResult
from ..core.logging import get_logger

logger = get_logger(__name__)

# Initialize Jinja2 templates
templates = Jinja2Templates(directory="app/web/templates")

router = APIRouter(tags=["Web UI"])


@router.get("/", response_class=HTMLResponse)
async def dashboard(request: Request, db: Session = Depends(get_db)):
    """Main dashboard page."""
    try:
        # Get basic statistics
        total_articles = db.query(NewsArticle).count()
        processed_articles = db.query(NewsArticle).filter(NewsArticle.is_processed == True).count()
        
        # Get recent task executions
        recent_tasks = db.query(TaskExecution).order_by(desc(TaskExecution.started_at)).limit(5).all()
        
        # Get latest summary
        latest_summary = db.query(TaskResult).filter(
            TaskResult.result_type == "daily_summary"
        ).order_by(desc(TaskResult.created_at)).first()
        
        return templates.TemplateResponse("dashboard.html", {
            "request": request,
            "total_articles": total_articles,
            "processed_articles": processed_articles,
            "unprocessed_articles": total_articles - processed_articles,
            "recent_tasks": recent_tasks,
            "latest_summary": latest_summary
        })
    except Exception as e:
        logger.error("Dashboard error", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/articles", response_class=HTMLResponse)
async def articles_page(
    request: Request,
    page: int = Query(1, ge=1),
    source: Optional[str] = Query(None),
    processed_only: Optional[bool] = Query(None),
    db: Session = Depends(get_db)
):
    """Articles listing page."""
    try:
        page_size = 20
        
        # Build query
        query = db.query(NewsArticle)
        
        if source:
            query = query.filter(NewsArticle.source_name.ilike(f"%{source}%"))
        
        if processed_only is not None:
            query = query.filter(NewsArticle.is_processed == processed_only)
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination
        articles = query.order_by(desc(NewsArticle.collected_at)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()
        
        # Get unique sources for filter
        sources = db.query(NewsArticle.source_name).distinct().all()
        sources = [s[0] for s in sources if s[0]]
        
        # Calculate pagination info
        total_pages = (total_count + page_size - 1) // page_size
        
        return templates.TemplateResponse("articles.html", {
            "request": request,
            "articles": articles,
            "sources": sources,
            "current_page": page,
            "total_pages": total_pages,
            "total_count": total_count,
            "page_size": page_size,
            "current_source": source,
            "processed_only": processed_only
        })
    except Exception as e:
        logger.error("Articles page error", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/articles/{article_id}", response_class=HTMLResponse)
async def article_detail(request: Request, article_id: int, db: Session = Depends(get_db)):
    """Article detail page."""
    try:
        article = db.query(NewsArticle).filter(NewsArticle.id == article_id).first()
        
        if not article:
            raise HTTPException(status_code=404, detail="Article not found")
        
        return templates.TemplateResponse("article_detail.html", {
            "request": request,
            "article": article
        })
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Article detail error", article_id=article_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/summaries", response_class=HTMLResponse)
async def summaries_page(
    request: Request,
    page: int = Query(1, ge=1),
    db: Session = Depends(get_db)
):
    """Daily summaries listing page."""
    try:
        page_size = 10
        
        # Get summaries
        query = db.query(TaskResult).filter(TaskResult.result_type == "daily_summary")
        total_count = query.count()
        
        summaries = query.order_by(desc(TaskResult.created_at)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()
        
        # Calculate pagination info
        total_pages = (total_count + page_size - 1) // page_size
        
        return templates.TemplateResponse("summaries.html", {
            "request": request,
            "summaries": summaries,
            "current_page": page,
            "total_pages": total_pages,
            "total_count": total_count,
            "page_size": page_size
        })
    except Exception as e:
        logger.error("Summaries page error", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/summaries/{summary_id}", response_class=HTMLResponse)
async def summary_detail(request: Request, summary_id: int, db: Session = Depends(get_db)):
    """Summary detail page."""
    try:
        summary = db.query(TaskResult).filter(
            TaskResult.id == summary_id,
            TaskResult.result_type == "daily_summary"
        ).first()
        
        if not summary:
            raise HTTPException(status_code=404, detail="Summary not found")
        
        return templates.TemplateResponse("summary_detail.html", {
            "request": request,
            "summary": summary
        })
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Summary detail error", summary_id=summary_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks", response_class=HTMLResponse)
async def tasks_page(
    request: Request,
    page: int = Query(1, ge=1),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Tasks listing page."""
    try:
        page_size = 20
        
        # Build query
        query = db.query(TaskExecution)
        
        if status:
            query = query.filter(TaskExecution.status == status)
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination
        tasks = query.order_by(desc(TaskExecution.started_at)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()
        
        # Calculate pagination info
        total_pages = (total_count + page_size - 1) // page_size
        
        return templates.TemplateResponse("tasks.html", {
            "request": request,
            "tasks": tasks,
            "current_page": page,
            "total_pages": total_pages,
            "total_count": total_count,
            "page_size": page_size,
            "current_status": status
        })
    except Exception as e:
        logger.error("Tasks page error", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", response_class=HTMLResponse)
async def task_detail(request: Request, task_id: int, db: Session = Depends(get_db)):
    """Task detail page."""
    try:
        task = db.query(TaskExecution).filter(TaskExecution.id == task_id).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return templates.TemplateResponse("task_detail.html", {
            "request": request,
            "task": task
        })
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Task detail error", task_id=task_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))
