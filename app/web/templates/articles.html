{% extends "base.html" %}

{% block title %}Articles - Carbon Regulation News{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">News Articles</h1>
        <p class="mt-2 text-gray-600"><PERSON>rowse collected carbon regulation news articles</p>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <form method="get" class="space-y-4 sm:space-y-0 sm:flex sm:items-end sm:space-x-4">
                <div class="flex-1">
                    <label for="source" class="block text-sm font-medium text-gray-700">Source</label>
                    <select name="source" id="source" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">All Sources</option>
                        {% for source in sources %}
                            <option value="{{ source }}" {% if current_source == source %}selected{% endif %}>{{ source }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="flex-1">
                    <label for="processed_only" class="block text-sm font-medium text-gray-700">Processing Status</label>
                    <select name="processed_only" id="processed_only" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">All Articles</option>
                        <option value="true" {% if processed_only == true %}selected{% endif %}>Processed Only</option>
                        <option value="false" {% if processed_only == false %}selected{% endif %}>Unprocessed Only</option>
                    </select>
                </div>
                <div>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Articles List -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                Articles ({{ total_count }} total)
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Page {{ current_page }} of {{ total_pages }}
            </p>
        </div>
        
        {% if articles %}
            <ul class="divide-y divide-gray-200">
                {% for article in articles %}
                    <li>
                        <a href="/articles/{{ article.id }}" class="block hover:bg-gray-50">
                            <div class="px-4 py-4 sm:px-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-blue-600 truncate">
                                            {{ article.title }}
                                        </p>
                                        <p class="mt-2 flex items-center text-sm text-gray-500">
                                            <span class="truncate">{{ article.source_name }}</span>
                                            <span class="mx-2">•</span>
                                            <span>{{ article.collected_at.strftime('%Y-%m-%d %H:%M') }}</span>
                                            {% if article.published_date %}
                                                <span class="mx-2">•</span>
                                                <span>Published: {{ article.published_date.strftime('%Y-%m-%d') }}</span>
                                            {% endif %}
                                        </p>
                                        {% if article.ai_summary %}
                                            <p class="mt-2 text-sm text-gray-700">
                                                {{ article.ai_summary[:200] }}{% if article.ai_summary|length > 200 %}...{% endif %}
                                            </p>
                                        {% endif %}
                                    </div>
                                    <div class="ml-4 flex-shrink-0 flex flex-col items-end space-y-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ 'bg-green-100 text-green-800' if article.is_processed else 'bg-yellow-100 text-yellow-800' }}">
                                            {{ 'Processed' if article.is_processed else 'Pending' }}
                                        </span>
                                        {% if article.ai_classification and article.ai_classification.get('category') %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ article.ai_classification.get('category') }}
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </a>
                    </li>
                {% endfor %}
            </ul>
        {% else %}
            <div class="px-4 py-12 text-center">
                <p class="text-gray-500">No articles found matching your criteria.</p>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if total_pages > 1 %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if current_page > 1 %}
                    <a href="?page={{ current_page - 1 }}{% if current_source %}&source={{ current_source }}{% endif %}{% if processed_only is not none %}&processed_only={{ processed_only }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}
                {% if current_page < total_pages %}
                    <a href="?page={{ current_page + 1 }}{% if current_source %}&source={{ current_source }}{% endif %}{% if processed_only is not none %}&processed_only={{ processed_only }}{% endif %}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ ((current_page - 1) * page_size) + 1 }}</span>
                        to
                        <span class="font-medium">{{ [current_page * page_size, total_count] | min }}</span>
                        of
                        <span class="font-medium">{{ total_count }}</span>
                        results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if current_page > 1 %}
                            <a href="?page={{ current_page - 1 }}{% if current_source %}&source={{ current_source }}{% endif %}{% if processed_only is not none %}&processed_only={{ processed_only }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                Previous
                            </a>
                        {% endif %}
                        
                        {% for page_num in range(1, total_pages + 1) %}
                            {% if page_num == current_page %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ page_num }}
                                </span>
                            {% elif page_num <= 3 or page_num > total_pages - 3 or (page_num >= current_page - 1 and page_num <= current_page + 1) %}
                                <a href="?page={{ page_num }}{% if current_source %}&source={{ current_source }}{% endif %}{% if processed_only is not none %}&processed_only={{ processed_only }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ page_num }}
                                </a>
                            {% elif page_num == 4 and current_page > 5 %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                            {% elif page_num == total_pages - 3 and current_page < total_pages - 4 %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                            {% endif %}
                        {% endfor %}
                        
                        {% if current_page < total_pages %}
                            <a href="?page={{ current_page + 1 }}{% if current_source %}&source={{ current_source }}{% endif %}{% if processed_only is not none %}&processed_only={{ processed_only }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                Next
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
