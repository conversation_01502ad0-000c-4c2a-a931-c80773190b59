{% extends "base.html" %}

{% block title %}Task #{{ task.id }} - Carbon Regulation News{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="/" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    Dashboard
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <span class="mx-2 text-gray-400">/</span>
                    <a href="/tasks" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">Tasks</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <span class="mx-2 text-gray-400">/</span>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Task #{{ task.id }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Task Header -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <h1 class="text-2xl font-bold text-gray-900 mb-4">{{ task.task_name }}</h1>
                    <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-4">
                        <span class="flex items-center">
                            <strong class="text-gray-700">Type:</strong>
                            <span class="ml-1">{{ task.task_type }}</span>
                        </span>
                        <span class="flex items-center">
                            <strong class="text-gray-700">Started:</strong>
                            <span class="ml-1">{{ task.started_at.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                        </span>
                        {% if task.completed_at %}
                            <span class="flex items-center">
                                <strong class="text-gray-700">Completed:</strong>
                                <span class="ml-1">{{ task.completed_at.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                            </span>
                        {% endif %}
                        {% if task.duration_seconds %}
                            <span class="flex items-center">
                                <strong class="text-gray-700">Duration:</strong>
                                <span class="ml-1">{{ "%.2f"|format(task.duration_seconds) }} seconds</span>
                            </span>
                        {% endif %}
                    </div>
                    <div class="flex flex-wrap gap-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ 'bg-green-100 text-green-800' if task.status == 'success' else 'bg-red-100 text-red-800' if task.status == 'failed' else 'bg-blue-100 text-blue-800' if task.status == 'running' else 'bg-gray-100 text-gray-800' }}">
                            {{ task.status.title() }}
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            {{ task.task_type.replace('_', ' ').title() }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Error Message -->
            {% if task.error_message %}
                <div class="bg-red-50 border border-red-200 rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-red-900 mb-4">Error Details</h3>
                        <div class="bg-red-100 p-4 rounded-md">
                            <p class="text-sm text-red-800 font-mono whitespace-pre-wrap">{{ task.error_message }}</p>
                        </div>
                        {% if task.error_details %}
                            <div class="mt-4">
                                <h4 class="text-sm font-medium text-red-900 mb-2">Additional Error Information</h4>
                                <div class="bg-red-100 p-4 rounded-md">
                                    <pre class="text-xs text-red-800 whitespace-pre-wrap">{{ task.error_details | tojson(indent=2) }}</pre>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}

            <!-- Result Summary -->
            {% if task.result_summary %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Execution Results</h3>
                        
                        {% if task.task_type == 'news_collection' %}
                            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                                {% if task.result_summary.get('saved_count') is not none %}
                                    <div class="bg-green-50 p-4 rounded-lg">
                                        <div class="text-2xl font-bold text-green-600">{{ task.result_summary.get('saved_count', 0) }}</div>
                                        <div class="text-sm text-green-800">New Articles</div>
                                    </div>
                                {% endif %}
                                {% if task.result_summary.get('total_collected') is not none %}
                                    <div class="bg-blue-50 p-4 rounded-lg">
                                        <div class="text-2xl font-bold text-blue-600">{{ task.result_summary.get('total_collected', 0) }}</div>
                                        <div class="text-sm text-blue-800">Total Collected</div>
                                    </div>
                                {% endif %}
                                {% if task.result_summary.get('sources') %}
                                    <div class="bg-purple-50 p-4 rounded-lg">
                                        <div class="text-2xl font-bold text-purple-600">{{ task.result_summary.get('sources')|length }}</div>
                                        <div class="text-sm text-purple-800">Sources</div>
                                    </div>
                                {% endif %}
                            </div>
                        {% elif task.task_type == 'ai_processing' %}
                            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                                {% if task.result_summary.get('processed_count') is not none %}
                                    <div class="bg-green-50 p-4 rounded-lg">
                                        <div class="text-2xl font-bold text-green-600">{{ task.result_summary.get('processed_count', 0) }}</div>
                                        <div class="text-sm text-green-800">Processed</div>
                                    </div>
                                {% endif %}
                                {% if task.result_summary.get('total_unprocessed') is not none %}
                                    <div class="bg-yellow-50 p-4 rounded-lg">
                                        <div class="text-2xl font-bold text-yellow-600">{{ task.result_summary.get('total_unprocessed', 0) }}</div>
                                        <div class="text-sm text-yellow-800">Total Unprocessed</div>
                                    </div>
                                {% endif %}
                                {% if task.result_summary.get('failed_count') is not none %}
                                    <div class="bg-red-50 p-4 rounded-lg">
                                        <div class="text-2xl font-bold text-red-600">{{ task.result_summary.get('failed_count', 0) }}</div>
                                        <div class="text-sm text-red-800">Failed</div>
                                    </div>
                                {% endif %}
                            </div>
                        {% elif task.task_type == 'daily_summary' %}
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                {% if task.result_summary.get('statistics', {}).get('total_articles') is not none %}
                                    <div class="bg-blue-50 p-4 rounded-lg">
                                        <div class="text-2xl font-bold text-blue-600">{{ task.result_summary.get('statistics', {}).get('total_articles', 0) }}</div>
                                        <div class="text-sm text-blue-800">Articles Analyzed</div>
                                    </div>
                                {% endif %}
                                {% if task.result_summary.get('statistics', {}).get('sources_count') is not none %}
                                    <div class="bg-green-50 p-4 rounded-lg">
                                        <div class="text-2xl font-bold text-green-600">{{ task.result_summary.get('statistics', {}).get('sources_count', 0) }}</div>
                                        <div class="text-sm text-green-800">Sources</div>
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}

                        <!-- Raw Result Summary -->
                        <div class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Raw Results</h4>
                            <div class="bg-gray-50 p-4 rounded-md">
                                <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ task.result_summary | tojson(indent=2) }}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Task Results -->
            {% if task.task_results %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Generated Results</h3>
                        <div class="space-y-4">
                            {% for result in task.task_results %}
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-start justify-between mb-2">
                                        <h4 class="font-medium text-gray-900">{{ result.title }}</h4>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ result.result_type }}
                                        </span>
                                    </div>
                                    {% if result.result_type == 'daily_summary' %}
                                        <div class="mb-2">
                                            <a href="/summaries/{{ result.id }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                View Full Summary →
                                            </a>
                                        </div>
                                    {% endif %}
                                    <p class="text-sm text-gray-600 mb-2">{{ result.summary[:200] }}{% if result.summary|length > 200 %}...{% endif %}</p>
                                    <div class="text-xs text-gray-500">
                                        Created: {{ result.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                                        {% if result.articles %}
                                            • {{ result.articles|length }} articles referenced
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Configuration -->
            {% if task.config_snapshot %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Configuration Snapshot</h3>
                        <div class="bg-gray-50 p-4 rounded-md">
                            <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ task.config_snapshot | tojson(indent=2) }}</pre>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Task Info -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Task Information</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Task ID</dt>
                            <dd class="text-sm text-gray-900">{{ task.id }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Name</dt>
                            <dd class="text-sm text-gray-900">{{ task.task_name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Type</dt>
                            <dd class="text-sm text-gray-900">{{ task.task_type }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="text-sm text-gray-900">{{ task.status }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Started At</dt>
                            <dd class="text-sm text-gray-900">{{ task.started_at.strftime('%Y-%m-%d %H:%M:%S') }}</dd>
                        </div>
                        {% if task.completed_at %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Completed At</dt>
                                <dd class="text-sm text-gray-900">{{ task.completed_at.strftime('%Y-%m-%d %H:%M:%S') }}</dd>
                            </div>
                        {% endif %}
                        {% if task.duration_seconds %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Duration</dt>
                                <dd class="text-sm text-gray-900">{{ "%.2f"|format(task.duration_seconds) }} seconds</dd>
                            </div>
                        {% endif %}
                    </dl>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-2">
                        <a href="/tasks" class="block w-full text-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            ← Back to Tasks
                        </a>
                        {% if task.task_results %}
                            {% for result in task.task_results %}
                                {% if result.result_type == 'daily_summary' %}
                                    <a href="/summaries/{{ result.id }}" class="block w-full text-center px-4 py-2 border border-blue-300 shadow-sm text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100">
                                        View Summary
                                    </a>
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
