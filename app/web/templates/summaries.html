{% extends "base.html" %}

{% block title %}Daily Summaries - Carbon Regulation News{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Daily Summaries</h1>
        <p class="mt-2 text-gray-600">AI-generated daily summaries of carbon regulation news</p>
    </div>

    <!-- Summaries List -->
    <div class="space-y-6">
        {% if summaries %}
            {% for summary in summaries %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-3">
                                    <h3 class="text-lg font-medium text-gray-900">
                                        {{ summary.title }}
                                    </h3>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ summary.created_at.strftime('%Y-%m-%d') }}
                                    </span>
                                </div>
                                
                                <div class="text-sm text-gray-500 mb-4">
                                    Generated: {{ summary.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    {% if summary.statistics and summary.statistics.get('total_articles') %}
                                        • {{ summary.statistics.get('total_articles') }} articles analyzed
                                    {% endif %}
                                </div>

                                <!-- Summary Preview -->
                                {% if summary.result_metadata and summary.result_metadata.get('markdown_summary') %}
                                    <div class="bg-gray-50 p-4 rounded-md mb-4">
                                        <div id="summary-{{ summary.id }}-markdown" data-markdown class="text-sm text-gray-700">
                                            {{ summary.result_metadata.get('markdown_summary')[:500] }}{% if summary.result_metadata.get('markdown_summary')|length > 500 %}...{% endif %}
                                        </div>
                                    </div>
                                {% elif summary.result_metadata and summary.result_metadata.get('executive_summary') %}
                                    <div class="bg-gray-50 p-4 rounded-md mb-4">
                                        <p class="text-sm text-gray-700">
                                            {{ summary.result_metadata.get('executive_summary')[:300] }}{% if summary.result_metadata.get('executive_summary')|length > 300 %}...{% endif %}
                                        </p>
                                    </div>
                                {% else %}
                                    <div class="bg-gray-50 p-4 rounded-md mb-4">
                                        <p class="text-sm text-gray-700">
                                            {{ summary.summary[:300] }}{% if summary.summary|length > 300 %}...{% endif %}
                                        </p>
                                    </div>
                                {% endif %}

                                <!-- Key Statistics -->
                                {% if summary.statistics %}
                                    <div class="flex flex-wrap gap-4 text-xs text-gray-500">
                                        {% if summary.statistics.get('total_articles') %}
                                            <span class="flex items-center">
                                                <span class="w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
                                                {{ summary.statistics.get('total_articles') }} articles
                                            </span>
                                        {% endif %}
                                        {% if summary.statistics.get('sources_count') %}
                                            <span class="flex items-center">
                                                <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                                                {{ summary.statistics.get('sources_count') }} sources
                                            </span>
                                        {% endif %}
                                        {% if summary.result_metadata and summary.result_metadata.get('clusters') %}
                                            <span class="flex items-center">
                                                <span class="w-2 h-2 bg-purple-500 rounded-full mr-1"></span>
                                                {{ summary.result_metadata.get('clusters')|length }} clusters
                                            </span>
                                        {% endif %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="ml-4 flex-shrink-0">
                                <a href="/summaries/{{ summary.id }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    View Full Summary
                                    <svg class="ml-2 -mr-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-12 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No summaries found</h3>
                    <p class="mt-1 text-sm text-gray-500">No daily summaries have been generated yet.</p>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if total_pages > 1 %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if current_page > 1 %}
                    <a href="?page={{ current_page - 1 }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}
                {% if current_page < total_pages %}
                    <a href="?page={{ current_page + 1 }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ ((current_page - 1) * page_size) + 1 }}</span>
                        to
                        <span class="font-medium">{{ [current_page * page_size, total_count] | min }}</span>
                        of
                        <span class="font-medium">{{ total_count }}</span>
                        results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if current_page > 1 %}
                            <a href="?page={{ current_page - 1 }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                Previous
                            </a>
                        {% endif %}
                        
                        {% for page_num in range(1, total_pages + 1) %}
                            {% if page_num == current_page %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ page_num }}
                                </span>
                            {% elif page_num <= 3 or page_num > total_pages - 3 or (page_num >= current_page - 1 and page_num <= current_page + 1) %}
                                <a href="?page={{ page_num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ page_num }}
                                </a>
                            {% elif page_num == 4 and current_page > 5 %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                            {% elif page_num == total_pages - 3 and current_page < total_pages - 4 %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                            {% endif %}
                        {% endfor %}
                        
                        {% if current_page < total_pages %}
                            <a href="?page={{ current_page + 1 }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                Next
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<script>
    // Render markdown content for summary previews
    document.addEventListener('DOMContentLoaded', function() {
        {% for summary in summaries %}
            renderMarkdown('summary-{{ summary.id }}-markdown');
        {% endfor %}
    });
</script>
{% endblock %}
