{% extends "base.html" %}

{% block title %}Dashboard - Carbon Regulation News{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p class="mt-2 text-gray-600">Overview of the Carbon Regulation News monitoring system</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Total Articles -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">📰</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Articles</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ total_articles }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Processed Articles -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">✅</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Processed Articles</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ processed_articles }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unprocessed Articles -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">⏳</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Unprocessed Articles</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ unprocessed_articles }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Latest Summary -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Latest Daily Summary</h3>
                {% if latest_summary %}
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm text-gray-500">Generated:</span>
                            <span class="text-sm text-gray-900">{{ latest_summary.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">{{ latest_summary.title }}</h4>
                        </div>
                        {% if latest_summary.result_metadata and latest_summary.result_metadata.get('markdown_summary') %}
                            <div class="bg-gray-50 p-4 rounded-md">
                                <div id="latest-summary-markdown" data-markdown class="text-sm text-gray-700">{{ latest_summary.result_metadata.get('markdown_summary') }}</div>
                            </div>
                        {% else %}
                            <div class="bg-gray-50 p-4 rounded-md">
                                <p class="text-sm text-gray-700">{{ latest_summary.summary[:300] }}{% if latest_summary.summary|length > 300 %}...{% endif %}</p>
                            </div>
                        {% endif %}
                        <div class="pt-2">
                            <a href="/summaries/{{ latest_summary.id }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                View Full Summary →
                            </a>
                        </div>
                    </div>
                {% else %}
                    <p class="text-gray-500">No daily summaries generated yet.</p>
                {% endif %}
            </div>
        </div>

        <!-- Recent Tasks -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Tasks</h3>
                {% if recent_tasks %}
                    <div class="space-y-3">
                        {% for task in recent_tasks %}
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm font-medium text-gray-900">{{ task.task_name }}</span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ 'bg-green-100 text-green-800' if task.status == 'success' else 'bg-red-100 text-red-800' if task.status == 'failed' else 'bg-blue-100 text-blue-800' if task.status == 'running' else 'bg-gray-100 text-gray-800' }}">
                                            {{ task.status }}
                                        </span>
                                    </div>
                                    <div class="text-xs text-gray-500 mt-1">
                                        {{ task.started_at.strftime('%Y-%m-%d %H:%M') }}
                                        {% if task.duration_seconds %}
                                            • {{ "%.1f"|format(task.duration_seconds) }}s
                                        {% endif %}
                                    </div>
                                </div>
                                <a href="/tasks/{{ task.id }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                    View →
                                </a>
                            </div>
                        {% endfor %}
                    </div>
                    <div class="mt-4 pt-4 border-t">
                        <a href="/tasks" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View All Tasks →
                        </a>
                    </div>
                {% else %}
                    <p class="text-gray-500">No tasks executed yet.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="/articles" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    📰 View Articles
                </a>
                <a href="/summaries" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    📊 View Summaries
                </a>
                <a href="/tasks" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    ⚙️ View Tasks
                </a>
                <a href="/docs" target="_blank" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    📖 API Docs
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // Render markdown content for the latest summary
    document.addEventListener('DOMContentLoaded', function() {
        renderMarkdown('latest-summary-markdown');
    });
</script>
{% endblock %}
