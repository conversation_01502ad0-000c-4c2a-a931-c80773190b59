{% extends "base.html" %}

{% block title %}Tasks - Carbon Regulation News{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Task Executions</h1>
        <p class="mt-2 text-gray-600">Monitor system task executions and their status</p>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <form method="get" class="space-y-4 sm:space-y-0 sm:flex sm:items-end sm:space-x-4">
                <div class="flex-1">
                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    <select name="status" id="status" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">All Statuses</option>
                        <option value="success" {% if current_status == 'success' %}selected{% endif %}>Success</option>
                        <option value="failed" {% if current_status == 'failed' %}selected{% endif %}>Failed</option>
                        <option value="running" {% if current_status == 'running' %}selected{% endif %}>Running</option>
                    </select>
                </div>
                <div>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Tasks List -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                Task Executions ({{ total_count }} total)
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Page {{ current_page }} of {{ total_pages }}
            </p>
        </div>
        
        {% if tasks %}
            <ul class="divide-y divide-gray-200">
                {% for task in tasks %}
                    <li>
                        <a href="/tasks/{{ task.id }}" class="block hover:bg-gray-50">
                            <div class="px-4 py-4 sm:px-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center space-x-3">
                                            <p class="text-sm font-medium text-blue-600 truncate">
                                                {{ task.task_name }}
                                            </p>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ 'bg-green-100 text-green-800' if task.status == 'success' else 'bg-red-100 text-red-800' if task.status == 'failed' else 'bg-blue-100 text-blue-800' if task.status == 'running' else 'bg-gray-100 text-gray-800' }}">
                                                {{ task.status }}
                                            </span>
                                        </div>
                                        <div class="mt-2 flex items-center text-sm text-gray-500 space-x-4">
                                            <span class="flex items-center">
                                                <strong class="text-gray-700">Type:</strong>
                                                <span class="ml-1">{{ task.task_type }}</span>
                                            </span>
                                            <span class="flex items-center">
                                                <strong class="text-gray-700">Started:</strong>
                                                <span class="ml-1">{{ task.started_at.strftime('%Y-%m-%d %H:%M') }}</span>
                                            </span>
                                            {% if task.completed_at %}
                                                <span class="flex items-center">
                                                    <strong class="text-gray-700">Completed:</strong>
                                                    <span class="ml-1">{{ task.completed_at.strftime('%Y-%m-%d %H:%M') }}</span>
                                                </span>
                                            {% endif %}
                                            {% if task.duration_seconds %}
                                                <span class="flex items-center">
                                                    <strong class="text-gray-700">Duration:</strong>
                                                    <span class="ml-1">{{ "%.1f"|format(task.duration_seconds) }}s</span>
                                                </span>
                                            {% endif %}
                                        </div>
                                        {% if task.error_message %}
                                            <div class="mt-2 text-sm text-red-600">
                                                <strong>Error:</strong> {{ task.error_message[:100] }}{% if task.error_message|length > 100 %}...{% endif %}
                                            </div>
                                        {% elif task.result_summary %}
                                            <div class="mt-2 text-sm text-gray-600">
                                                {% if task.task_type == 'news_collection' and task.result_summary.get('saved_count') %}
                                                    Collected {{ task.result_summary.get('saved_count') }} new articles
                                                {% elif task.task_type == 'ai_processing' and task.result_summary.get('processed_count') %}
                                                    Processed {{ task.result_summary.get('processed_count') }} articles
                                                {% elif task.task_type == 'daily_summary' and task.result_summary.get('statistics', {}).get('total_articles') %}
                                                    Generated summary for {{ task.result_summary.get('statistics', {}).get('total_articles') }} articles
                                                {% elif task.task_type == 'full_pipeline' %}
                                                    Full pipeline execution completed
                                                {% else %}
                                                    Task completed successfully
                                                {% endif %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="ml-4 flex-shrink-0">
                                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </li>
                {% endfor %}
            </ul>
        {% else %}
            <div class="px-4 py-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No tasks found</h3>
                <p class="mt-1 text-sm text-gray-500">No task executions match your criteria.</p>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if total_pages > 1 %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if current_page > 1 %}
                    <a href="?page={{ current_page - 1 }}{% if current_status %}&status={{ current_status }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}
                {% if current_page < total_pages %}
                    <a href="?page={{ current_page + 1 }}{% if current_status %}&status={{ current_status }}{% endif %}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ ((current_page - 1) * page_size) + 1 }}</span>
                        to
                        <span class="font-medium">{{ [current_page * page_size, total_count] | min }}</span>
                        of
                        <span class="font-medium">{{ total_count }}</span>
                        results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if current_page > 1 %}
                            <a href="?page={{ current_page - 1 }}{% if current_status %}&status={{ current_status }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                Previous
                            </a>
                        {% endif %}
                        
                        {% for page_num in range(1, total_pages + 1) %}
                            {% if page_num == current_page %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ page_num }}
                                </span>
                            {% elif page_num <= 3 or page_num > total_pages - 3 or (page_num >= current_page - 1 and page_num <= current_page + 1) %}
                                <a href="?page={{ page_num }}{% if current_status %}&status={{ current_status }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ page_num }}
                                </a>
                            {% elif page_num == 4 and current_page > 5 %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                            {% elif page_num == total_pages - 3 and current_page < total_pages - 4 %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                            {% endif %}
                        {% endfor %}
                        
                        {% if current_page < total_pages %}
                            <a href="?page={{ current_page + 1 }}{% if current_status %}&status={{ current_status }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                Next
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
