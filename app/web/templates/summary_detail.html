{% extends "base.html" %}

{% block title %}{{ summary.title }} - Carbon Regulation News{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="/" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    Dashboard
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <span class="mx-2 text-gray-400">/</span>
                    <a href="/summaries" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">Summaries</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <span class="mx-2 text-gray-400">/</span>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{{ summary.created_at.strftime('%Y-%m-%d') }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Summary Header -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <h1 class="text-2xl font-bold text-gray-900 mb-4">{{ summary.title }}</h1>
                    <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-4">
                        <span class="flex items-center">
                            <strong class="text-gray-700">Generated:</strong>
                            <span class="ml-1">{{ summary.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        </span>
                        {% if summary.statistics and summary.statistics.get('total_articles') %}
                            <span class="flex items-center">
                                <strong class="text-gray-700">Articles Analyzed:</strong>
                                <span class="ml-1">{{ summary.statistics.get('total_articles') }}</span>
                            </span>
                        {% endif %}
                        {% if summary.statistics and summary.statistics.get('sources_count') %}
                            <span class="flex items-center">
                                <strong class="text-gray-700">Sources:</strong>
                                <span class="ml-1">{{ summary.statistics.get('sources_count') }}</span>
                            </span>
                        {% endif %}
                    </div>
                    <div class="flex flex-wrap gap-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Daily Summary
                        </span>
                        {% if summary.result_metadata and summary.result_metadata.get('clusters') %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                {{ summary.result_metadata.get('clusters')|length }} Clusters
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-3 space-y-6">
            <!-- Markdown Summary -->
            {% if summary.result_metadata and summary.result_metadata.get('markdown_summary') %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Summary Report</h3>
                        <div id="full-markdown-summary" data-markdown class="prose prose-sm max-w-none" style="display: none;">{{ summary.result_metadata.get('markdown_summary') }}</div>
                        <div id="full-markdown-summary-rendered" class="prose prose-sm max-w-none"></div>
                    </div>
                </div>
            {% endif %}

            <!-- Executive Summary (fallback) -->
            {% if summary.result_metadata and summary.result_metadata.get('executive_summary') and not summary.result_metadata.get('markdown_summary') %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Executive Summary</h3>
                        <div class="prose prose-sm max-w-none">
                            <p class="text-gray-700">{{ summary.result_metadata.get('executive_summary') }}</p>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Key Developments -->
            {% if summary.result_metadata and summary.result_metadata.get('key_developments') %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Key Developments</h3>
                        <ul class="space-y-3">
                            {% for development in summary.result_metadata.get('key_developments') %}
                                <li class="flex items-start">
                                    <span class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></span>
                                    <span class="text-gray-700">{{ development }}</span>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            {% endif %}

            <!-- Regulatory Changes -->
            {% if summary.result_metadata and summary.result_metadata.get('regulatory_changes') %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Regulatory Changes</h3>
                        <ul class="space-y-3">
                            {% for change in summary.result_metadata.get('regulatory_changes') %}
                                <li class="flex items-start">
                                    <span class="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2 mr-3"></span>
                                    <span class="text-gray-700">{{ change }}</span>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            {% endif %}

            <!-- Market Implications -->
            {% if summary.result_metadata and summary.result_metadata.get('market_implications') %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Market Implications</h3>
                        <ul class="space-y-3">
                            {% for implication in summary.result_metadata.get('market_implications') %}
                                <li class="flex items-start">
                                    <span class="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></span>
                                    <span class="text-gray-700">{{ implication }}</span>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            {% endif %}

            <!-- Article Clusters -->
            {% if summary.result_metadata and summary.result_metadata.get('clusters') %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Article Clusters</h3>
                        <div class="space-y-4">
                            {% for cluster in summary.result_metadata.get('clusters') %}
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-start justify-between mb-2">
                                        <h4 class="font-medium text-gray-900">{{ cluster.get('theme', 'Unknown Theme') }}</h4>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ 'bg-red-100 text-red-800' if cluster.get('priority') == 'High' else 'bg-yellow-100 text-yellow-800' if cluster.get('priority') == 'Medium' else 'bg-green-100 text-green-800' }}">
                                            {{ cluster.get('priority', 'Medium') }} Priority
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">{{ cluster.get('description', '') }}</p>
                                    <div class="text-xs text-gray-500">
                                        {{ cluster.get('article_indices', [])|length }} articles in this cluster
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Fallback Content -->
            {% if not summary.result_metadata or not summary.result_metadata.get('markdown_summary') %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Summary Content</h3>
                        <div class="prose prose-sm max-w-none">
                            <div class="text-gray-700 whitespace-pre-wrap">{{ summary.summary }}</div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Statistics -->
            {% if summary.statistics %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Statistics</h3>
                        <dl class="space-y-3">
                            {% for key, value in summary.statistics.items() %}
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">{{ key.replace('_', ' ').title() }}</dt>
                                    <dd class="text-sm text-gray-900">{{ value }}</dd>
                                </div>
                            {% endfor %}
                        </dl>
                    </div>
                </div>
            {% endif %}

            <!-- Important Dates -->
            {% if summary.result_metadata and summary.result_metadata.get('important_dates') %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Important Dates</h3>
                        <ul class="space-y-2">
                            {% for date_item in summary.result_metadata.get('important_dates') %}
                                <li class="text-sm text-gray-700">{{ date_item }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            {% endif %}

            <!-- Key Findings -->
            {% if summary.key_findings %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Key Findings</h3>
                        <ul class="space-y-2">
                            {% for finding in summary.key_findings %}
                                <li class="text-sm text-gray-700">• {{ finding }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            {% endif %}

            <!-- Raw JSON Data -->
            {% if summary.result_metadata %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Raw JSON Data</h3>
                        <div id="json-metadata" style="display: none;">{{ summary.result_metadata | tojson }}</div>
                        <div id="json-metadata-rendered"></div>
                    </div>
                </div>
            {% endif %}

            <!-- Summary Info -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Summary Info</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Summary ID</dt>
                            <dd class="text-sm text-gray-900">{{ summary.id }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Type</dt>
                            <dd class="text-sm text-gray-900">{{ summary.result_type }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Generated</dt>
                            <dd class="text-sm text-gray-900">{{ summary.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Render markdown content
    document.addEventListener('DOMContentLoaded', function() {
        // Check if marked library is loaded
        if (typeof marked === 'undefined') {
            console.error('Marked library not loaded');
            return;
        }

        // Render markdown
        const sourceElement = document.getElementById('full-markdown-summary');
        const targetElement = document.getElementById('full-markdown-summary-rendered');

        if (sourceElement && targetElement) {
            const markdownText = sourceElement.textContent || sourceElement.innerText;

            // Clean up escaped characters
            const cleanedText = markdownText
                .replace(/\\n/g, '\n')
                .replace(/\\t/g, '\t')
                .replace(/\\"/g, '"')
                .replace(/\\'/g, "'")
                .replace(/\\r/g, '\r');

            // Parse markdown and set HTML
            const htmlContent = marked.parse(cleanedText);
            targetElement.innerHTML = htmlContent;
            targetElement.classList.add('markdown-content');

            console.log('Rendered markdown summary');
        }

        // Render JSON
        const jsonSourceElement = document.getElementById('json-metadata');
        const jsonTargetElement = document.getElementById('json-metadata-rendered');

        if (jsonSourceElement && jsonTargetElement) {
            try {
                const jsonText = jsonSourceElement.textContent || jsonSourceElement.innerText;
                const jsonData = JSON.parse(jsonText);
                const formattedJSON = JSON.stringify(jsonData, null, 2);

                jsonTargetElement.innerHTML = `<pre class="bg-gray-100 p-4 rounded-lg overflow-x-auto text-xs"><code class="language-json">${escapeHtml(formattedJSON)}</code></pre>`;
                jsonTargetElement.classList.add('json-content');

                console.log('Rendered JSON metadata');
            } catch (e) {
                console.error('Failed to parse JSON metadata:', e);
                jsonTargetElement.innerHTML = '<p class="text-red-500 text-sm">Error parsing JSON data</p>';
            }
        }

        // Helper function to escape HTML
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    });
</script>
{% endblock %}
