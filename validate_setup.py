#!/usr/bin/env python3
"""
Setup validation script for Carbon Regulation News Application.
This script validates that the environment is correctly configured.
"""

import sys
import os
import subprocess
from pathlib import Path
import importlib.util

def check_python_version():
    """Check Python version."""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 11:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.11+")
        return False

def check_dependencies():
    """Check if all required dependencies are installed."""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        'fastapi', 'uvicorn', 'pydantic', 'sqlalchemy', 
        'pydantic_ai', 'openai', 'tavily', 'requests', 
        'httpx', 'schedule', 'python_dotenv', 'yaml',
        'pytest', 'structlog', 'feedparser'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            # Handle package name variations
            import_name = package
            if package == 'python_dotenv':
                import_name = 'dotenv'
            elif package == 'yaml':
                import_name = 'yaml'
            elif package == 'tavily':
                import_name = 'tavily'
            elif package == 'pydantic_ai':
                import_name = 'pydantic_ai'
                
            __import__(import_name)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies installed")
    return True

def check_environment_file():
    """Check if .env file exists and has required variables."""
    print("\n🔧 Checking environment configuration...")
    
    env_file = Path('.env')
    if not env_file.exists():
        print("⚠️  .env file not found")
        print("Copy .env.example to .env and configure your API keys")
        return False
    
    print("✅ .env file exists")
    
    # Check for required environment variables
    required_vars = ['TAVILY_API_KEY', 'OPENROUTER_API_KEY']
    missing_vars = []
    
    with open('.env', 'r') as f:
        content = f.read()
        for var in required_vars:
            if f"{var}=" not in content or f"{var}=your_" in content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Missing or placeholder API keys: {', '.join(missing_vars)}")
        print("Update your .env file with actual API keys")
        return False
    
    print("✅ Required environment variables configured")
    return True

def check_app_imports():
    """Check if the application modules can be imported."""
    print("\n🚀 Checking application imports...")
    
    try:
        import app.api.main
        print("✅ Main API module imports successfully")
    except Exception as e:
        print(f"❌ Failed to import main API module: {e}")
        return False
    
    try:
        import app.scripts.run_daily_task
        print("✅ Daily task script imports successfully")
    except Exception as e:
        print(f"❌ Failed to import daily task script: {e}")
        return False
    
    try:
        import app.core.database
        print("✅ Database module imports successfully")
    except Exception as e:
        print(f"❌ Failed to import database module: {e}")
        return False
    
    return True

def check_database():
    """Check database connectivity."""
    print("\n🗄️ Checking database...")
    
    try:
        from app.core.database import DatabaseManager
        DatabaseManager.initialize()
        print("✅ Database initialization successful")
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def check_tests():
    """Check if tests can be discovered."""
    print("\n🧪 Checking test configuration...")
    
    try:
        result = subprocess.run(
            [sys.executable, '-m', 'pytest', '--collect-only', '-q'],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            test_count = len([line for line in result.stdout.split('\n') if '::' in line])
            print(f"✅ {test_count} tests discovered successfully")
            return True
        else:
            print(f"❌ Test discovery failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Test check failed: {e}")
        return False

def check_docker():
    """Check Docker availability."""
    print("\n🐳 Checking Docker (optional)...")
    
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {result.stdout.strip()}")
            
            # Check Docker Compose
            result = subprocess.run(['docker-compose', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {result.stdout.strip()}")
                return True
            else:
                print("⚠️  Docker Compose not available")
                return False
        else:
            print("⚠️  Docker not available")
            return False
    except FileNotFoundError:
        print("⚠️  Docker not installed")
        return False

def main():
    """Run all validation checks."""
    print("🔍 Carbon Regulation News - Setup Validation")
    print("=" * 50)
    
    checks = [
        check_python_version,
        check_dependencies,
        check_environment_file,
        check_app_imports,
        check_database,
        check_tests,
        check_docker
    ]
    
    results = []
    for check in checks:
        try:
            result = check()
            results.append(result)
        except Exception as e:
            print(f"❌ Check failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)
    
    passed = sum(results[:6])  # First 6 are required
    total_required = 6
    docker_available = results[6] if len(results) > 6 else False
    
    if passed == total_required:
        print("🎉 ALL REQUIRED CHECKS PASSED!")
        print("✅ Your environment is ready to run the application")
        
        if docker_available:
            print("✅ Docker is available for containerized deployment")
        else:
            print("⚠️  Docker not available (optional)")
        
        print("\n🚀 Next steps:")
        print("  1. Start the application: python -m app.api.main")
        print("  2. Run tests: pytest")
        print("  3. Access API docs: http://localhost:8000/docs")
        
        return 0
    else:
        print(f"❌ {total_required - passed} required checks failed")
        print("Please fix the issues above before running the application")
        return 1

if __name__ == "__main__":
    sys.exit(main())
