# Development Docker Compose Configuration
# Use this for local development with hot reloading

version: '3.8'

services:
  # Development application service
  app-dev:
    build: 
      context: .
      dockerfile: Dockerfile.dev
    container_name: carbon-regulation-news-dev
    ports:
      - "8000:8000"
    environment:
      # Development settings
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      
      # Database configuration (SQLite for development)
      - DATABASE__URL=sqlite:///./data/carbon_news_dev.db
      - DATABASE__ECHO=true
      
      # API server settings
      - API__HOST=0.0.0.0
      - API__PORT=8000
      - API__DEBUG=true
      
      # News collection settings
      - NEWS_COLLECTOR__MAX_ARTICLES_PER_SOURCE=5
      - NEWS_COLLECTOR__DEFAULT_TIME_RANGE=day
      
      # Scheduler settings
      - SCHEDULER__DAILY_RUN_TIME=09:00
      - SCHEDULER__MAX_TASK_HISTORY=50
      - SCHEDULER__TASK_TIMEOUT_MINUTES=15
      
      # API Keys (set these in .env file)
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
    
    volumes:
      # Mount source code for hot reloading
      - .:/app
      - ./data:/app/data
      - ./logs:/app/logs
      # Exclude Python cache and virtual environment
      - /app/__pycache__
      - /app/.pytest_cache
      - /app/venv
    
    restart: unless-stopped
    
    command: ["uvicorn", "app.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    
    networks:
      - carbon-news-dev-network

networks:
  carbon-news-dev-network:
    driver: bridge
