# Git
.git
.gitignore
.gitattributes

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
htmlcov

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
*.log
logs/
.env
.env.local
.env.*.local

# Database files
*.db
*.sqlite
*.sqlite3

# Documentation
README.md
SETUP.md
docs/

# Tests
tests/
.pytest_cache/

# Development files
docker-compose.dev.yml
Dockerfile.dev

# Temporary files
tmp/
temp/
*.tmp
