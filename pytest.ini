[tool:pytest]
filterwarnings =
    # Ignore all Pydantic v2 migration warnings
    ignore::pydantic.warnings.PydanticDeprecatedSince20
    # Ignore Pydantic deprecation warnings from internal config
    ignore:Support for class-based.*config.*is deprecated:pydantic.warnings.PydanticDeprecatedSince20
    # Ignore specific pydantic internal config warnings from third-party libraries
    ignore:Support for class-based.*config.*is deprecated.*use ConfigDict instead:DeprecationWarning:pydantic._internal._config
    # Ignore asyncio event loop warnings from pydantic_graph
    ignore:There is no current event loop:DeprecationWarning:pydantic_graph._utils
    ignore:There is no current event loop:DeprecationWarning
    # Ignore datetime.utcnow warnings from pydantic itself
    ignore:datetime.datetime.utcnow\(\) is deprecated:DeprecationWarning:pydantic.main
