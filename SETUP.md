# Carbon Regulation News - Setup Guide

This guide provides complete setup instructions for the Carbon Regulation News application, a FastAPI-based system for collecting, analyzing, and summarizing carbon regulation and climate policy news using AI.

## Prerequisites

- **Python 3.13.7** (confirmed working version)
- **Git** for version control
- **Docker & Docker Compose** (optional, for containerized deployment)
- **API Keys** (required for full functionality):
  - [Tavily API Key](https://tavily.com) - for web search and content extraction
  - [OpenRouter API Key](https://openrouter.ai) - for AI processing

## Quick Start

### 1. Clone and Setup Environment

```bash
# Clone the repository
git clone <repository-url>
cd carbon-regulation-news

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Linux/macOS:
source venv/bin/activate
# On Windows:
# venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configure Environment Variables

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your API keys
nano .env  # or use your preferred editor
```

**Required configuration in `.env`:**
```bash
# API Keys (REQUIRED)
TAVILY_API_KEY=your_tavily_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Application Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Database (SQLite by default)
DATABASE__URL=sqlite:///./carbon_news.db

# API Server
API__HOST=0.0.0.0
API__PORT=8000
```

### 3. Initialize Database

```bash
# Initialize fresh database
python reset_database.py
# Type 'yes' when prompted
```

### 4. Run the Application

```bash
# Start the FastAPI server
python -m app.api.main

# Alternative: Using uvicorn directly
uvicorn app.api.main:app --host 0.0.0.0 --port 8000 --reload
```

The application will be available at:
- **API**: http://localhost:8000
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Running Tests

### Basic Test Execution

```bash
# Run all tests
pytest

# Run with verbose output
pytest -v

# Run with coverage report
pytest --cov=app --cov-report=html

# Run specific test categories
pytest tests/test_api/          # API tests only
pytest tests/test_services/     # Service tests only
pytest tests/test_integration/  # Integration tests only
```

### Test Configuration

The project uses pytest with the following configuration:
- **Test Database**: In-memory SQLite for isolation
- **Fixtures**: Comprehensive test fixtures in `tests/conftest.py`
- **Mocking**: External APIs (Tavily, OpenRouter) are mocked
- **Coverage**: Configured for detailed coverage reporting

## Manual Operations

### News Collection and Processing

```bash
# Run complete pipeline (collection + processing + summary)
python app/scripts/run_daily_task.py --task-type full-pipeline

# Run individual tasks
python app/scripts/run_daily_task.py --task-type collection
python app/scripts/run_daily_task.py --task-type processing
python app/scripts/run_daily_task.py --task-type summary

# Dry run (see what would be executed)
python app/scripts/run_daily_task.py --task-type full-pipeline --dry-run

# Verbose output
python app/scripts/run_daily_task.py --task-type full-pipeline --verbose
```

### Database Management

```bash
# Reset database (removes all data)
python reset_database.py

# Check database status via API
curl http://localhost:8000/health/database
```

## API Usage Examples

### Health Checks

```bash
# Basic health check
curl http://localhost:8000/health

# Detailed system statistics
curl http://localhost:8000/health/statistics

# Database status
curl http://localhost:8000/health/database
```

### Task Management

```bash
# Trigger news collection
curl -X POST http://localhost:8000/tasks/trigger \
  -H "Content-Type: application/json" \
  -d '{"task_type": "news_collection", "task_name": "manual_collection"}'

# Check recent task executions
curl http://localhost:8000/tasks/recent

# Get task execution details
curl http://localhost:8000/tasks/executions/{task_id}
```

### News and Summaries

```bash
# Get recent articles
curl http://localhost:8000/news/articles?limit=10

# Get daily summaries
curl http://localhost:8000/news/summaries

# Get specific article
curl http://localhost:8000/news/articles/{article_id}
```

## Docker Deployment

### Simple Docker Setup

The project includes a simplified Docker configuration for easy containerized deployment:

```bash
# Build and start the application
docker-compose up --build

# Run in background
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop services
docker-compose down
```

**Docker Configuration:**
- **Single service**: Just the FastAPI application
- **SQLite database**: Persisted in `./data` volume
- **Python 3.13.7**: As specified in requirements
- **Health checks**: Built-in container health monitoring
- **Environment variables**: Configured via .env file

### Manual Docker Build

```bash
# Build image
docker build -t carbon-regulation-news .

# Run container with environment variables
docker run -p 8000:8000 \
  -e TAVILY_API_KEY=your_key \
  -e OPENROUTER_API_KEY=your_key \
  -v $(pwd)/data:/app/data \
  carbon-regulation-news
```

**Note**: Docker build includes system dependencies and may take several minutes on first build.

## Configuration Details

### Environment Variables

The application supports extensive configuration via environment variables. Key settings:

- **API Keys**: `TAVILY_API_KEY`, `OPENROUTER_API_KEY`
- **Database**: `DATABASE__URL`, `DATABASE__ECHO`
- **API Server**: `API__HOST`, `API__PORT`, `API__DEBUG`
- **Scheduler**: `SCHEDULER__DAILY_RUN_TIME`, `SCHEDULER__MAX_TASK_HISTORY`
- **News Collection**: `NEWS_COLLECTOR__MAX_ARTICLES_PER_SOURCE`

### YAML Configuration

Advanced settings are configured in `config.yaml`:
- AI model settings and prompts
- News source configuration
- Notification settings
- Monitoring and alerts

## Troubleshooting

### Common Issues

1. **Missing API Keys**
   ```bash
   # Error: API key not configured
   # Solution: Set TAVILY_API_KEY and OPENROUTER_API_KEY in .env
   ```

2. **Database Issues**
   ```bash
   # Reset database if corrupted
   python reset_database.py
   ```

3. **Port Already in Use**
   ```bash
   # Change port in .env or kill existing process
   API__PORT=8001
   ```

4. **Import Errors**
   ```bash
   # Ensure virtual environment is activated
   source venv/bin/activate
   pip install -r requirements.txt
   ```

### Logs and Debugging

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Check application logs
tail -f logs/app.log  # if file logging is configured

# API request logging
export API__DEBUG=true
```

## Development Workflow

### Code Quality

```bash
# Format code
black app/ tests/

# Lint code
flake8 app/ tests/

# Run tests with coverage
pytest --cov=app --cov-report=term-missing
```

### Adding New Features

1. Create feature branch
2. Add tests first (TDD approach)
3. Implement functionality
4. Run full test suite
5. Update documentation

## Production Deployment

### Environment Setup

```bash
# Production environment variables
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=WARNING
DATABASE__URL=********************************/carbon_news
```

### Security Considerations

- Use PostgreSQL for production database
- Configure proper CORS origins
- Set up SSL/TLS termination
- Use secrets management for API keys
- Configure monitoring and alerting

## Validation Script

Use the included validation script to verify your setup:

```bash
# Run setup validation
python validate_setup.py

# This will check:
# - Python version (3.11+ required, 3.13.7 recommended)
# - All dependencies installed
# - Environment variables configured
# - Application imports working
# - Database connectivity
# - Test discovery (76 tests found)
# - Docker availability (optional)
```

## Project Structure

```
carbon-regulation-news/
├── app/                       # Main application package
│   ├── api/                   # FastAPI routes and schemas
│   │   ├── main.py           # Application entry point
│   │   └── routes/           # API endpoint implementations
│   ├── core/                 # Core functionality
│   │   ├── config.py         # Configuration management
│   │   ├── database.py       # Database setup and models
│   │   └── logging.py        # Logging configuration
│   ├── services/             # Business logic services
│   │   ├── news_collector.py # News collection service
│   │   ├── ai_parser.py      # AI processing service
│   │   └── scheduler.py      # Task scheduling service
│   └── scripts/              # Utility scripts
│       └── run_daily_task.py # Manual task execution
├── tests/                    # Test suite (76 tests)
│   ├── test_api/            # API endpoint tests
│   ├── test_services/       # Service layer tests
│   └── test_integration/    # End-to-end tests
├── config.yaml              # Application configuration
├── requirements.txt         # Python dependencies
├── pytest.ini              # Test configuration
├── Dockerfile               # Production container
├── docker-compose.yml       # Multi-service deployment
└── validate_setup.py        # Setup validation script
```

## Verified Commands

All commands below have been tested and verified to work:

### Application Startup
```bash
# Method 1: Direct Python execution
python -m app.api.main

# Method 2: Using uvicorn
uvicorn app.api.main:app --host 0.0.0.0 --port 8000 --reload
```

### Test Execution (76 tests available)
```bash
# Run all tests
pytest                        # ✅ Verified: 76 tests collected

# Run with verbose output
pytest -v

# Run specific test categories
pytest tests/test_api/        # API tests
pytest tests/test_services/   # Service tests
pytest tests/test_integration/ # Integration tests

# Run with coverage
pytest --cov=app --cov-report=html
```

### Manual Task Execution
```bash
# Full pipeline (collection + processing + summary)
python app/scripts/run_daily_task.py --task-type full-pipeline

# Individual tasks
python app/scripts/run_daily_task.py --task-type collection
python app/scripts/run_daily_task.py --task-type processing
python app/scripts/run_daily_task.py --task-type summary

# With options
python app/scripts/run_daily_task.py --task-type full-pipeline --verbose --dry-run
```

## Support

For issues and questions:
1. Run `python validate_setup.py` to check your environment
2. Check this setup guide
3. Review API documentation at `/docs`
4. Check application logs
5. Verify environment configuration
6. Test with minimal configuration first
